# LinkedIn 工作流限流功能使用说明

## 🎯 功能概述

该限流系统确保 LinkedIn 工作流程每分钟最多执行 30 次，超出限制的请求将自动排队等待执行。

## 📊 核心特性

- **限流控制**: 每分钟最多 30 次执行（每 2 秒 1 次）
- **智能排队**: 最大队列容量 1000 个任务
- **异步支持**: 支持异步和同步两种调用方式
- **状态监控**: 提供实时队列状态查询
- **故障处理**: 完整的错误处理和统计功能

## 🚀 API 端点

### 1. 异步提交任务
```bash
POST /api/v1/workflow/async
```

**参数:**
- `email`: 邮箱地址
- `url`: LinkedIn URL

**示例:**
```bash
curl -X POST "http://localhost:8080/api/v1/workflow/async" \
  -d "email=<EMAIL>" \
  -d "url=https://linkedin.com/in/username"
```

**响应:**
```json
{
  "status": "success",
  "message": "Task submitted successfully",
  "email": "<EMAIL>",
  "url": "https://linkedin.com/in/username",
  "queueSize": 5
}
```

### 2. 同步提交任务（带超时）
```bash
POST /api/v1/workflow/sync
```

**参数:**
- `email`: 邮箱地址
- `url`: LinkedIn URL
- `timeoutMinutes`: 超时时间（分钟，默认 10）

**示例:**
```bash
curl -X POST "http://localhost:8080/api/v1/workflow/sync" \
  -d "email=<EMAIL>" \
  -d "url=https://linkedin.com/in/username" \
  -d "timeoutMinutes=15"
```

**响应:**
```json
{
  "status": "success",
  "message": "Workflow completed successfully",
  "email": "<EMAIL>",
  "url": "https://linkedin.com/in/username",
  "result": "talent-id-12345"
}
```

### 3. 查询队列状态
```bash
GET /api/v1/workflow/status
```

**示例:**
```bash
curl "http://localhost:8080/api/v1/workflow/status"
```

**响应:**
```json
{
  "queueSize": 8,
  "queueCapacity": 1000,
  "totalSubmitted": 150,
  "totalProcessed": 142,
  "totalFailed": 3,
  "rateLimit": "30 requests/minute"
}
```

## 📈 使用场景

### 场景 1: 批量处理（推荐异步）
```java
// 批量提交多个任务
for (MailingListItem item : mailingList) {
    CompletableFuture<String> future = rateLimitedWorkflowService
        .submitWorkflowAsync(item.getEmail(), item.getUrl());
    
    // 可选：处理结果
    future.thenAccept(result -> {
        log.info("处理完成: {}", result);
    });
}
```

### 场景 2: 实时处理（同步）
```java
try {
    String result = rateLimitedWorkflowService
        .submitWorkflowSync(email, url, 10); // 10分钟超时
    
    return ResponseEntity.ok(result);
} catch (Exception e) {
    return ResponseEntity.badRequest().body(e.getMessage());
}
```

### 场景 3: 状态监控
```java
QueueStatus status = rateLimitedWorkflowService.getQueueStatus();
if (status.getQueueSize() > 900) {
    log.warn("队列接近满载: {}/{}", status.getQueueSize(), status.getQueueCapacity());
}
```

## ⚙️ 配置说明

### 限流参数
- **执行频率**: 30 次/分钟 (0.5 次/秒)
- **队列容量**: 1000 个任务
- **线程模型**: 单线程顺序处理

### 超时设置
- **默认超时**: 10 分钟
- **最大建议超时**: 30 分钟
- **队列等待**: 无限制（直到队列满）

## 🔍 监控指标

| 指标 | 说明 |
|------|------|
| `queueSize` | 当前队列中等待的任务数 |
| `queueCapacity` | 队列最大容量 |
| `totalSubmitted` | 累计提交的任务数 |
| `totalProcessed` | 累计成功处理的任务数 |
| `totalFailed` | 累计失败的任务数 |

## ⚠️ 注意事项

1. **队列满载**: 当队列达到 1000 个任务时，新提交的任务会被拒绝
2. **超时处理**: 同步调用超时后任务仍会继续执行，但调用方会收到超时异常
3. **服务重启**: 服务重启时队列中的任务会丢失
4. **内存占用**: 大量排队任务会占用内存，建议监控队列状态

## 🛠️ 故障排查

### 常见错误

1. **队列已满**
   ```json
   {
     "status": "error",
     "message": "Task queue is full, please try again later"
   }
   ```
   **解决方案**: 等待队列消化或增加队列容量

2. **执行超时**
   ```json
   {
     "status": "error", 
     "message": "Workflow execution timed out after 10 minutes"
   }
   ```
   **解决方案**: 增加超时时间或检查任务执行效率

3. **工作流执行失败**
   ```json
   {
     "status": "error",
     "message": "Workflow execution failed: [具体错误信息]"
   }
   ```
   **解决方案**: 检查输入参数和网络连接

## 📊 性能建议

- **批量处理**: 优先使用异步方式
- **实时处理**: 少量任务可使用同步方式
- **监控队列**: 定期检查队列状态，避免队列积压
- **合理超时**: 根据实际网络情况设置合理的超时时间 