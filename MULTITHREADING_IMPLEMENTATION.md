# LinkedIn 批量爬取多线程实现

## 🎯 概述

本次更新将原本的单线程 LinkedIn 批量爬取功能升级为多线程并发处理，使用 **3 个线程** 并发执行，在保持限流控制的同时显著提升处理速度。

## 🚀 主要改进

### 1. 多线程架构
- **线程池大小**: 固定 3 个线程
- **并发控制**: 使用 `Semaphore` 控制最大并发数量
- **任务分发**: 使用 `CompletableFuture` 异步执行任务
- **同步等待**: 批次内所有任务完成后再进行下一批次

### 2. 线程池配置
创建了专用的线程池配置类 `ThreadPoolConfig.java`:
```java
@Bean(name = "batchLinkedinScrapeExecutor")
public Executor batchLinkedinScrapeExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(3);           // 核心线程数：3
    executor.setMaxPoolSize(3);            // 最大线程数：3
    executor.setQueueCapacity(100);        // 队列容量：100
    executor.setThreadNamePrefix("batch-linkedin-");
    // ... 其他配置
    return executor;
}
```

### 3. 限流机制保持
- **限流速率**: 保持每分钟 30 次请求 (1 request/second)
- **限流实现**: 继续使用 Google Guava 的 `RateLimiter`
- **线程安全**: 每个线程在处理前都需要获取限流器许可

## 📊 性能提升

### 理论性能提升
- **单线程**: 1 个任务/秒 (受限流限制)
- **多线程**: 最多 3 个任务并发，但仍受限流控制
- **实际提升**: 在网络延迟较高的情况下，多线程可以更好地利用等待时间

### 处理流程对比

#### 原单线程流程:
```
任务1 → 限流等待 → 处理 → 任务2 → 限流等待 → 处理 → ...
```

#### 新多线程流程:
```
任务1 ┐
任务2 ├→ 并发处理 (最多3个) → 各自限流等待 → 各自处理
任务3 ┘
```

## 🔧 技术实现细节

### 1. 核心类修改
- **`BatchLinkedinScrapeService`**: 主要业务逻辑类
  - 添加线程池执行器注入
  - 添加信号量控制并发
  - 重构批处理逻辑使用 `CompletableFuture`

### 2. 新增配置类
- **`ThreadPoolConfig`**: 线程池配置
  - 专用于批量 LinkedIn 爬取的线程池
  - 合理的队列大小和拒绝策略

### 3. 线程安全处理
- 使用 `AtomicInteger` 进行线程安全的计数
- 使用 `Semaphore` 控制并发数量
- 保持原有的限流机制

## 📝 使用方式

### 命令行启动
```bash
# 交互式模式
java -jar worker-0.0.1-SNAPSHOT.jar

# 自动模式
java -jar worker-0.0.1-SNAPSHOT.jar auto
```

### 配置信息
系统启动时会显示新的配置信息:
```
📋 This tool will:
   1. Fetch unscraped data from database (1000 records per batch)
   2. Process records using 3 concurrent threads
   3. Apply rate limiting (30 requests per minute)
   4. Continue until all data is processed
```

## 🔍 监控和日志

### 线程标识
每个日志条目都包含线程名称，便于跟踪:
```
🔄 Thread batch-linkedin-1 processing item 1/1000 - Email: <EMAIL>
✅ Thread batch-linkedin-2 successfully processed: <EMAIL> -> talent-12345
```

### 状态监控
`getProcessingStatus()` 方法新增多线程相关信息:
- `threadPoolSize`: 线程池大小 (3)
- `concurrentThreads`: 并发线程描述
- `semaphoreAvailablePermits`: 当前可用的信号量许可数

## ⚠️ 注意事项

### 1. 限流仍然有效
- 虽然使用多线程，但每个请求仍需遵守限流规则
- 总体请求频率不会超过每分钟 30 次

### 2. 资源使用
- 3 个线程会增加内存和 CPU 使用
- 数据库连接池需要支持并发连接

### 3. 错误处理
- 单个线程的错误不会影响其他线程
- 批次级别的错误统计和报告

## 🧪 测试

### 单元测试更新
- 更新了 `BatchLinkedinScrapeServiceTest`
- 添加了线程池执行器的 Mock
- 使用 `lenient()` 避免不必要的 stubbing 警告

### 集成测试
建议在实际环境中测试:
1. 验证多线程并发处理
2. 确认限流机制正常工作
3. 监控系统资源使用情况

## 🔮 未来优化建议

1. **动态线程池大小**: 根据系统负载动态调整线程数
2. **更智能的限流**: 基于 API 响应时间动态调整限流速率
3. **监控仪表板**: 添加实时监控界面显示处理进度和线程状态
4. **故障恢复**: 添加更完善的错误恢复和重试机制

---

## 📋 变更文件清单

### 新增文件
- `src/main/java/ai/beyz/worker/config/ThreadPoolConfig.java`
- `MULTITHREADING_IMPLEMENTATION.md`

### 修改文件
- `src/main/java/ai/beyz/worker/service/BatchLinkedinScrapeService.java`
- `src/main/java/ai/beyz/worker/cli/LinkedinScrapeCliApplication.java`
- `src/test/java/ai/beyz/worker/service/BatchLinkedinScrapeServiceTest.java`
- `pom.xml`

### 主要变更
1. 实现多线程并发处理 (3 线程)
2. 保持限流机制不变
3. 添加线程安全的统计和日志
4. 更新帮助信息和状态显示
5. 修复测试兼容性问题
