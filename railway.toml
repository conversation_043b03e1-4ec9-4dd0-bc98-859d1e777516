[build]
builder = "dockerfile"
dockerfilePath = "Dockerfile"

[deploy]
startCommand = "java -Djava.security.egd=file:/dev/./urandom -Dspring.profiles.active=production -jar app.jar"
healthcheckPath = "/actuator/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[env]
# 确保生产环境配置
SPRING_PROFILES_ACTIVE = "production"
JAVA_OPTS = "-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0" 