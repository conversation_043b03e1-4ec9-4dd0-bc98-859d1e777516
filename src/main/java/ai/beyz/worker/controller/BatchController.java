package ai.beyz.worker.controller;

import ai.beyz.worker.service.RateLimitedWorkflowService;
import ai.beyz.worker.service.SendDataToQueueService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/v1")
@Slf4j
public class BatchController {

    @Autowired
    private SendDataToQueueService sendDataToQueueService;

    //@Autowired
    //private RateLimitedWorkflowService rateLimitedWorkflowService;

    @GetMapping("/send-data-to-queue")
    public String sendDataToQueue(@RequestParam(name = "limit", defaultValue = "100") int limit) {
        sendDataToQueueService.sendDataToQueue(limit);
        return "success";
    }

    /**
     * 查询队列状态
     */
   /* @GetMapping("/workflow/status")
    public ResponseEntity<RateLimitedWorkflowService.QueueStatus> getQueueStatus() {
        return ResponseEntity.ok(rateLimitedWorkflowService.getQueueStatus());
    }*/

    // 响应数据类
    public static class WorkflowResponse {
        public String status;
        public String message;
        public String email;
        public String url;
        public int queueSize;

        public WorkflowResponse(String status, String message, String email, String url, int queueSize) {
            this.status = status;
            this.message = message;
            this.email = email;
            this.url = url;
            this.queueSize = queueSize;
        }
    }

    public static class WorkflowResult {
        public String status;
        public String message;
        public String email;
        public String url;
        public String result;

        public WorkflowResult(String status, String message, String email, String url, String result) {
            this.status = status;
            this.message = message;
            this.email = email;
            this.url = url;
            this.result = result;
        }
    }

    public static class ErrorResponse {
        public String status;
        public String message;

        public ErrorResponse(String status, String message) {
            this.status = status;
            this.message = message;
        }
    }

}
