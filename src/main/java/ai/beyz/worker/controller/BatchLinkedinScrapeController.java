package ai.beyz.worker.controller;

import ai.beyz.worker.service.BatchLinkedinScrapeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/v1/batch")
@Slf4j
public class BatchLinkedinScrapeController {

    @Autowired
    private BatchLinkedinScrapeService batchLinkedinScrapeService;

    private volatile boolean isProcessing = false;
    private CompletableFuture<Void> currentProcessingTask = null;

    /**
     * 启动批量LinkedIn数据爬取（异步）
     */
    @PostMapping("/linkedin-scrape/start")
    public ResponseEntity<Map<String, Object>> startBatchScraping() {
        Map<String, Object> response = new HashMap<>();
        
        if (isProcessing) {
            response.put("status", "error");
            response.put("message", "Batch processing is already running");
            response.put("isProcessing", true);
            return ResponseEntity.badRequest().body(response);
        }
        
        try {
            isProcessing = true;
            
            // 异步执行批处理
            currentProcessingTask = CompletableFuture.runAsync(() -> {
                try {
                    log.info("🚀 Starting batch LinkedIn scraping via API...");
                    batchLinkedinScrapeService.processBatchLinkedinScrape();
                    log.info("✅ Batch LinkedIn scraping completed via API");
                } catch (Exception e) {
                    log.error("❌ Batch LinkedIn scraping failed via API: {}", e.getMessage(), e);
                } finally {
                    isProcessing = false;
                }
            });
            
            response.put("status", "success");
            response.put("message", "Batch processing started successfully");
            response.put("isProcessing", true);
            response.put("note", "This is an asynchronous operation. Use /status endpoint to check progress.");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            isProcessing = false;
            log.error("❌ Failed to start batch processing: {}", e.getMessage(), e);
            
            response.put("status", "error");
            response.put("message", "Failed to start batch processing: " + e.getMessage());
            response.put("isProcessing", false);
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 停止批量处理（尝试优雅停止）
     */
    @PostMapping("/linkedin-scrape/stop")
    public ResponseEntity<Map<String, Object>> stopBatchScraping() {
        Map<String, Object> response = new HashMap<>();
        
        if (!isProcessing) {
            response.put("status", "info");
            response.put("message", "No batch processing is currently running");
            response.put("isProcessing", false);
            return ResponseEntity.ok(response);
        }
        
        try {
            if (currentProcessingTask != null) {
                boolean cancelled = currentProcessingTask.cancel(true);
                
                response.put("status", cancelled ? "success" : "warning");
                response.put("message", cancelled ? 
                    "Batch processing stop requested" : 
                    "Failed to cancel batch processing, it may complete naturally");
                response.put("cancelled", cancelled);
                response.put("note", "The current item being processed will complete before stopping");
            } else {
                response.put("status", "warning");
                response.put("message", "No active processing task found");
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error stopping batch processing: {}", e.getMessage(), e);
            
            response.put("status", "error");
            response.put("message", "Error stopping batch processing: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取批处理状态
     */
    @GetMapping("/linkedin-scrape/status")
    public ResponseEntity<Map<String, Object>> getBatchStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取基础状态
            Map<String, Object> serviceStatus = batchLinkedinScrapeService.getProcessingStatus();
            response.putAll(serviceStatus);
            
            // 添加运行时状态
            response.put("isProcessing", isProcessing);
            response.put("hasActiveTask", currentProcessingTask != null && !currentProcessingTask.isDone());
            
            if (currentProcessingTask != null) {
                response.put("taskStatus", currentProcessingTask.isDone() ? "completed" : "running");
                response.put("taskCancelled", currentProcessingTask.isCancelled());
                response.put("taskCompletedExceptionally", currentProcessingTask.isCompletedExceptionally());
            }
            
            response.put("status", "success");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error getting batch status: {}", e.getMessage(), e);
            
            response.put("status", "error");
            response.put("message", "Error getting batch status: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取批处理配置信息
     */
    @GetMapping("/linkedin-scrape/config")
    public ResponseEntity<Map<String, Object>> getBatchConfig() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            response.put("status", "success");
            response.put("batchSize", 1000);
            response.put("rateLimit", "30 requests per minute");
            response.put("rateLimitDetails", "0.5 requests per second (1 request every 2 seconds)");
            response.put("description", "Batch LinkedIn scraping with rate limiting");
            response.put("workflow", "findUnscrapedWithEmailAndLinkedinUrl -> LinkedinScrapeServiceV2.workflow");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error getting batch config: {}", e.getMessage(), e);
            
            response.put("status", "error");
            response.put("message", "Error getting batch config: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/linkedin-scrape/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            response.put("status", "healthy");
            response.put("service", "BatchLinkedinScrapeService");
            response.put("isProcessing", isProcessing);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Health check failed: {}", e.getMessage(), e);
            
            response.put("status", "unhealthy");
            response.put("error", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
