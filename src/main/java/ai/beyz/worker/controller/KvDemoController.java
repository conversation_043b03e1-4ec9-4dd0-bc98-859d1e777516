package ai.beyz.worker.controller;

import ai.beyz.worker.config.CloudflareKvClient;
import ai.beyz.worker.service.CloudflareKvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * Cloudflare KV 多namespace操作演示控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/kv-demo")
public class KvDemoController {

    private final CloudflareKvClient kvClient;
    private final CloudflareKvService kvService;

    @Autowired
    public KvDemoController(CloudflareKvClient kvClient, CloudflareKvService kvService) {
        this.kvClient = kvClient;
        this.kvService = kvService;
    }

    // ==================== 默认namespace操作演示 ====================

    /**
     * 演示使用默认namespace的操作
     */
    @PostMapping("/default-namespace")
    public Mono<ResponseEntity<String>> demoDefaultNamespace(@RequestBody Map<String, String> request) {
        String key = request.get("key");
        String value = request.get("value");
        
        log.info("演示默认namespace操作: key={}, value={}", key, value);
        
        return kvClient.put(key, value)  // 使用默认namespace
                .flatMap(success -> {
                    if (success) {
                        return kvClient.get(key).map(retrievedValue -> 
                            ResponseEntity.ok("成功存储并获取: " + retrievedValue));
                    } else {
                        return Mono.just(ResponseEntity.badRequest().body("存储失败"));
                    }
                });
    }

    // ==================== 指定namespace操作演示 ====================

    /**
     * 演示指定namespace ID的操作
     */
    @PostMapping("/namespace/{namespaceId}")
    public Mono<ResponseEntity<String>> demoSpecificNamespace(
            @PathVariable String namespaceId,
            @RequestBody Map<String, String> request) {
        
        String key = request.get("key");
        String value = request.get("value");
        
        log.info("演示指定namespace操作: namespace={}, key={}, value={}", namespaceId, key, value);
        
        return kvClient.put(namespaceId, key, value)  // 直接指定namespace ID
                .flatMap(success -> {
                    if (success) {
                        return kvClient.get(namespaceId, key).map(retrievedValue -> 
                            ResponseEntity.ok("成功存储并获取: " + retrievedValue));
                    } else {
                        return Mono.just(ResponseEntity.badRequest().body("存储失败"));
                    }
                });
    }

    // ==================== 通过别名操作演示 ====================

    /**
     * 演示通过namespace别名的操作
     */
    @PostMapping("/alias/{namespaceAlias}")
    public Mono<ResponseEntity<String>> demoNamespaceAlias(
            @PathVariable String namespaceAlias,
            @RequestBody Map<String, String> request) {
        
        String key = request.get("key");
        String value = request.get("value");
        
        log.info("演示别名namespace操作: alias={}, key={}, value={}", namespaceAlias, key, value);
        
        return kvClient.putByAlias(namespaceAlias, key, value)  // 通过别名操作
                .flatMap(success -> {
                    if (success) {
                        return kvClient.getByAlias(namespaceAlias, key).map(retrievedValue -> 
                            ResponseEntity.ok("成功存储并获取: " + retrievedValue));
                    } else {
                        return Mono.just(ResponseEntity.badRequest().body("存储失败"));
                    }
                })
                .onErrorResume(error -> {
                    log.error("别名操作失败", error);
                    return Mono.just(ResponseEntity.badRequest().body("别名不存在或操作失败: " + error.getMessage()));
                });
    }

    // ==================== 批量操作演示 ====================

    /**
     * 演示批量操作不同namespace
     */
    @PostMapping("/batch-demo")
    public Mono<ResponseEntity<String>> demoBatchOperations(@RequestBody Map<String, Object> request) {
        String namespaceAlias = (String) request.get("namespace");
        @SuppressWarnings("unchecked")
        Map<String, String> dataMap = (Map<String, String>) request.get("data");
        
        log.info("演示批量操作: namespace={}, dataSize={}", namespaceAlias, dataMap.size());
        
        // 构建批量数据
        List<CloudflareKvClient.KvPair> kvPairs = dataMap.entrySet().stream()
                .map(entry -> {
                    CloudflareKvClient.KvPair kvPair = new CloudflareKvClient.KvPair();
                    kvPair.setKey(entry.getKey());
                    kvPair.setValue(entry.getValue());
                    kvPair.setExpirationTtl(3600); // 1小时过期
                    return kvPair;
                })
                .toList();
        
        return kvClient.bulkPutByAlias(namespaceAlias, kvPairs)
                .map(response -> ResponseEntity.ok(
                    String.format("批量操作完成: 成功=%d, 失败=%d", 
                        response.getSuccessfulKeyCount(),
                        response.getUnsuccessfulKeys() != null ? response.getUnsuccessfulKeys().size() : 0)
                ))
                .onErrorResume(error -> {
                    log.error("批量操作失败", error);
                    return Mono.just(ResponseEntity.badRequest().body("批量操作失败: " + error.getMessage()));
                });
    }

    // ==================== 业务服务演示 ====================

    /**
     * 演示缓存服务
     */
    @PostMapping("/cache-demo")
    public Mono<ResponseEntity<String>> demoCacheService(@RequestBody Map<String, String> request) {
        String key = request.get("key");
        String data = request.get("data");
        
        log.info("演示缓存服务: key={}", key);
        
        return kvService.cacheData(key, data, 600)  // 10分钟缓存
                .flatMap(success -> {
                    if (success) {
                        return kvService.getCachedData(key).map(cachedData -> 
                            ResponseEntity.ok("缓存成功，数据: " + cachedData));
                    } else {
                        return Mono.just(ResponseEntity.badRequest().body("缓存失败"));
                    }
                });
    }

    /**
     * 演示会话服务
     */
    @PostMapping("/session-demo")
    public Mono<ResponseEntity<String>> demoSessionService(@RequestBody Map<String, String> request) {
        String sessionId = request.get("sessionId");
        String sessionData = request.get("sessionData");
        
        log.info("演示会话服务: sessionId={}", sessionId);
        
        return kvService.storeUserSession(sessionId, sessionData)
                .flatMap(success -> {
                    if (success) {
                        return kvService.getUserSession(sessionId).map(data -> 
                            ResponseEntity.ok("会话存储成功，数据: " + data));
                    } else {
                        return Mono.just(ResponseEntity.badRequest().body("会话存储失败"));
                    }
                });
    }

    /**
     * 演示用户数据批量操作
     */
    @PostMapping("/user-data-demo")
    public Mono<ResponseEntity<String>> demoUserDataService(@RequestBody Map<String, String> userDataMap) {
        log.info("演示用户数据批量操作: 用户数量={}", userDataMap.size());
        
        return kvService.batchStoreUserData(userDataMap)
                .map(response -> ResponseEntity.ok(
                    String.format("用户数据批量存储完成: 成功数量=%d", response.getSuccessfulKeyCount())
                ));
    }

    /**
     * 演示数据迁移
     */
    @PostMapping("/migrate-demo")
    public Mono<ResponseEntity<String>> demoDataMigration(@RequestBody Map<String, String> request) {
        String sourceNamespace = request.get("sourceNamespace");
        String targetNamespace = request.get("targetNamespace");
        String key = request.get("key");
        
        log.info("演示数据迁移: {} -> {}, key={}", sourceNamespace, targetNamespace, key);
        
        return kvService.migrateDataBetweenNamespaces(sourceNamespace, targetNamespace, key)
                .map(success -> {
                    if (success) {
                        return ResponseEntity.ok("数据迁移成功");
                    } else {
                        return ResponseEntity.badRequest().body("数据迁移失败");
                    }
                });
    }

    // ==================== 管理接口 ====================

    /**
     * 获取所有配置的namespace
     */
    @GetMapping("/namespaces")
    public ResponseEntity<Map<String, String>> getAllNamespaces() {
        Map<String, String> namespaces = kvService.getAllNamespaces();
        log.info("获取所有namespace: {}", namespaces.keySet());
        return ResponseEntity.ok(namespaces);
    }

    /**
     * 检查namespace是否存在
     */
    @GetMapping("/check-namespace/{alias}")
    public ResponseEntity<Map<String, Object>> checkNamespace(@PathVariable String alias) {
        boolean exists = kvService.isNamespaceExists(alias);
        Map<String, Object> result = Map.of(
            "alias", alias,
            "exists", exists
        );
        return ResponseEntity.ok(result);
    }

    /**
     * 列出指定namespace的键
     */
    @GetMapping("/keys/{namespaceAlias}")
    public Mono<ResponseEntity<String>> listNamespaceKeys(@PathVariable String namespaceAlias) {
        log.info("列出namespace键: {}", namespaceAlias);
        
        return kvService.listKeysByNamespace(namespaceAlias)
                .map(keys -> ResponseEntity.ok(keys))
                .onErrorResume(error -> {
                    log.error("列出键失败", error);
                    return Mono.just(ResponseEntity.badRequest().body("列出键失败: " + error.getMessage()));
                });
    }

    /**
     * 健康检查 - 测试所有namespace连通性
     */
    @GetMapping("/health-check")
    public Mono<ResponseEntity<Map<String, Object>>> healthCheck() {
        log.info("执行KV健康检查");
        
        Map<String, String> namespaces = kvService.getAllNamespaces();
        String testKey = "health_check_" + System.currentTimeMillis();
        String testValue = "ok";
        
        return kvClient.put(testKey, testValue)  // 测试默认namespace
                .map(success -> {
                    Map<String, Object> result = Map.of(
                        "status", success ? "healthy" : "unhealthy",
                        "namespaces", namespaces,
                        "testKey", testKey,
                        "timestamp", System.currentTimeMillis()
                    );
                    return ResponseEntity.ok(result);
                })
                .onErrorResume(error -> {
                    log.error("健康检查失败", error);
                    Map<String, Object> result = Map.of(
                        "status", "error",
                        "error", error.getMessage(),
                        "timestamp", System.currentTimeMillis()
                    );
                    return Mono.just(ResponseEntity.badRequest().body(result));
                });
    }
} 