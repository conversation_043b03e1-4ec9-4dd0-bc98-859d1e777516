package ai.beyz.worker.entity.profile;

import ai.beyz.worker.config.VectorConverter;
import com.alibaba.fastjson2.annotation.JSONField;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Table(name = "linkedin_profiles")
@ToString
public class LinkedinProfile {
    @Id
    private String id;
    
    private String urn;
    
    @Column(name = "public_identifier")
    private String publicIdentifier;
    
    @Column(name = "first_name")
    private String firstName;
    
    @Column(name = "last_name")
    private String lastName;
    
    @Column(name = "full_name")
    private String fullName;
    
    private String headline;
    
    @Column(name = "is_premium")
    private Boolean isPremium;
    
    @Column(name = "is_open_to_work")
    private Boolean isOpenToWork;
    
    @Column(name = "is_hiring")
    private Boolean isHiring;
    
    @Column(name = "is_memorialized")
    private Boolean isMemorialized;
    
    @Column(name = "is_influencer")
    private Boolean isInfluencer;
    
    @Column(name = "is_top_voice")
    private Boolean isTopVoice;
    
    @Column(name = "is_creator")
    private Boolean isCreator;
    
    @Column(name = "birth_day")
    private Integer birthDay;
    
    @Column(name = "birth_month")
    private Integer birthMonth;
    
    @Column(name = "birth_year")
    private Integer birthYear;
    
    private String pronoun;
    
    private Long created;
    
    @Column(name = "created_date")
    private LocalDateTime createdDate;
    
    @Column(name = "location_country")
    private String locationCountry;
    
    @Column(name = "location_country_code")
    private String locationCountryCode;
    
    @Column(name = "location_city")
    private String locationCity;
    
    @JdbcTypeCode(SqlTypes.JSON)
    @JSONField(serialize = false, deserialize = false)
    private String avatar; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    @JSONField(serialize = false, deserialize = false)
    private String cover; // JSONB stored as String

    @Column(name = "associated_hashtag")
    private String[] associatedHashtag; // TEXT[] array type
    
    @Column(name = "website_title")
    @JSONField(serialize = false, deserialize = false)
    private String websiteTitle;
    
    @Column(name = "website_url")
    @JSONField(serialize = false, deserialize = false)
    private String websiteUrl;
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "supported_locales")
    private String supportedLocales; // JSONB stored as String
    
    @Column(name = "primary_locale_country")
    private String primaryLocaleCountry;
    
    @Column(name = "primary_locale_language")
    private String primaryLocaleLanguage;
    
    @Column(name = "follower_count")
    private Long followerCount;
    
    @Column(name = "connection_count")
    private Long connectionCount;
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String experiences; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String skills; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String certifications; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String publications; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String educations; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String honors; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    private String volunteers; // JSONB stored as String


    @JSONField(serialize = false, deserialize = false)
    private String email;
    
    @Column(name = "linkedin_url")
    @JSONField(serialize = false, deserialize = false)
    private String linkedinUrl;
    
    private String gender;
    
    @Column(name = "current_title")
    private String currentTitle;
    
    @Column(name = "current_job_duration_years")
    private Double currentJobDurationYears;
    
    private Integer age;
    
    @Column(name = "updated_date")
    private LocalDateTime updatedDate;
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "company_list")
    private String companyList; // JSONB stored as String
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "school_list")
    private String schoolList; // JSONB stored as String
    
    private String industry;
    
    @Column(name = "is_student")
    private Boolean isStudent;
    
    @Column(name = "graduation_year")
    private Integer graduationYear;
    
    @Column(name = "highest_degree")
    private String highestDegree;
    
    private Double yoe; // years of experience
    
    @Column(name = "field_of_study")
    private String fieldOfStudy;
    
    @Column(name = "current_company")
    private String currentCompany;
    
    private String summary;
    
    @Column(name = "current_company_size")
    private String currentCompanySize;

    //@Column(name = "embedding_small", columnDefinition = "vector(1536)")
    //@Convert(converter = VectorConverter.class)
    //private float[] embeddingSmall; // 使用 float[] 数组存储向量数据
    
    private Boolean enriched = false; // DEFAULT false
}
