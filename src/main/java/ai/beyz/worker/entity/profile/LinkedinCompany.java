package ai.beyz.worker.entity.profile;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "linkedin_companies")
public class LinkedinCompany {
    
    @Id
    @Column(name = "company_id")
    private String companyId;
    
    @Column(name = "universal_name")
    private String universalName;
    
    private String name;
    
    @Column(name = "company_size")
    private String companySize;
    
    private String industry;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 在保存前自动设置更新时间
     */
    @PrePersist
    @PreUpdate
    public void prePersist() {
        this.updatedAt = LocalDateTime.now();
    }
}
