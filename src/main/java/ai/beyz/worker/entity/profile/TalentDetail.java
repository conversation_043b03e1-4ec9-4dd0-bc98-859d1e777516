package ai.beyz.worker.entity.profile;

import com.alibaba.fastjson2.annotation.JSONField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.stringtemplate.v4.compiler.CodeGenerator.primary_return;

@Data
@Entity
@Table(name = "talent_detail")
@ToString
public class TalentDetail {

    @Id
    @Column(name = "talent_id")
    private String talentId;

    @JdbcTypeCode(SqlTypes.JSON)
    @JSONField(serialize = false, deserialize = false)
    private String avatar; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    @JSONField(serialize = false, deserialize = false)
    private String cover; // JSONB stored as String
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "supported_locales")
    private String supportedLocales; // JSONB stored as String
    @Column(name = "associated_hashtag")
    private String[] associatedHashtag; // TEXT[] array type

    @JdbcTypeCode(SqlTypes.JSON)
    private String experiences; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String skills; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String certifications; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String publications; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String educations; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String honors; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    private String volunteers; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "company_list")
    private String companyList; // JSONB stored as String

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "school_list")
    private String schoolList; // JSONB stored as String

    private String summary;

    private String originText;
}
