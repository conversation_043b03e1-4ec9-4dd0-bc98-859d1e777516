package ai.beyz.worker.entity.beyz;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.sql.Timestamp;

@Entity
@Table(name = "mailingList")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MailingList {
    
    @Id
    @Column(name = "email", nullable = false, length = 255)
    private String email;
    
    @Column(name = "name", length = 255)
    private String name;
    
    @Column(name = "unsubscribe", nullable = false)
    private Boolean unsubscribe = false;
    
    @Column(name = "title", length = 255)
    private String title;
    
    @Column(name = "linkedinURL", length = 255)
    private String linkedinUrl;
    
    @Column(name = "send48HourEmail", nullable = false)
    private Boolean send48HourEmail = false;
    
    @Column(name = "send120MinEmail", nullable = false)
    private Boolean send120MinEmail = false;
    
    @Column(name = "scraped")
    private Boolean scraped = false;

    @Column(name = "createdAt")
    private Timestamp createdAt;
    @Column(name = "updatedAt")
    private Timestamp updatedAt;
    @Column(name = "status")
    private int status;
    @Column(name = "comments")
    private String comments;
}
