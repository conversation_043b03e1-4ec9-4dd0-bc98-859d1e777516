package ai.beyz.worker.cli;

import ai.beyz.worker.service.BatchLinkedinScrapeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.util.Scanner;

@SpringBootApplication
@ComponentScan(basePackages = "ai.beyz.worker")
@Slf4j
public class LinkedinScrapeCliApplication implements CommandLineRunner {

    @Autowired
    private BatchLinkedinScrapeService batchLinkedinScrapeService;

    public static void main(String[] args) {
        // 设置Spring Boot为非Web应用
        System.setProperty("spring.main.web-application-type", "none");
        
        log.info("🚀 Starting LinkedIn Scrape CLI Application...");
        SpringApplication.run(LinkedinScrapeCliApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=".repeat(80));
        log.info("🔧 LinkedIn Batch Scraping Tool");
        log.info("=".repeat(80));
        log.info("📋 This tool will:");
        log.info("   1. Fetch unscraped data from database (1000 records per batch)");
        log.info("   2. Process records using 3 concurrent threads");
        log.info("   3. Apply rate limiting (30 requests per minute)");
        log.info("   4. Continue until all data is processed");
        log.info("=".repeat(80));
        
        // 显示配置信息
        var status = batchLinkedinScrapeService.getProcessingStatus();
        log.info("⚙️ Configuration:");
        log.info("   - Rate Limit: {}", status.get("rateLimitConfig"));
        log.info("   - Batch Size: {}", status.get("batchSize"));
        log.info("=".repeat(80));
        
        // 如果有命令行参数，直接运行
        if (args.length > 0 && "auto".equals(args[0])) {
            log.info("🤖 Auto mode detected. Starting batch processing immediately...");
            startBatchProcessing();
            return;
        }
        
        // 交互式模式
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            log.info("📝 Available commands:");
            log.info("   1. start  - Start batch processing");
            log.info("   2. status - Show current status");
            log.info("   3. help   - Show this help message");
            log.info("   4. exit   - Exit the application");
            log.info("-".repeat(40));
            
            System.out.print("Enter command: ");
            String command = scanner.nextLine().trim().toLowerCase();
            
            switch (command) {
                case "start":
                case "1":
                    startBatchProcessing();
                    break;
                    
                case "status":
                case "2":
                    showStatus();
                    break;
                    
                case "help":
                case "3":
                    showHelp();
                    break;
                    
                case "exit":
                case "4":
                case "quit":
                case "q":
                    log.info("👋 Goodbye!");
                    return;
                    
                default:
                    log.warn("❌ Unknown command: {}. Type 'help' for available commands.", command);
                    break;
            }
            
            log.info(""); // 空行分隔
        }
    }
    
    private void startBatchProcessing() {
        try {
            log.info("🚀 Starting batch LinkedIn scraping process...");
            log.info("⚠️  This process may take a long time depending on the amount of data.");
            log.info("⚠️  The process will respect rate limits (30 requests/minute).");
            log.info("⚠️  Press Ctrl+C to stop the process if needed.");
            log.info("-".repeat(60));
            
            long startTime = System.currentTimeMillis();
            batchLinkedinScrapeService.processBatchLinkedinScrape();
            long endTime = System.currentTimeMillis();
            
            log.info("=".repeat(60));
            log.info("🎉 Batch processing completed successfully!");
            log.info("⏱️  Total execution time: {:.2f} minutes", (endTime - startTime) / 60000.0);
            log.info("=".repeat(60));
            
        } catch (Exception e) {
            log.error("❌ Batch processing failed: {}", e.getMessage(), e);
            log.error("💡 Please check the logs above for more details.");
        }
    }
    
    private void showStatus() {
        log.info("📊 Current Status:");
        var status = batchLinkedinScrapeService.getProcessingStatus();
        status.forEach((key, value) -> log.info("   - {}: {}", key, value));
    }
    
    private void showHelp() {
        log.info("📖 LinkedIn Batch Scraping Tool Help");
        log.info("-".repeat(40));
        log.info("🎯 Purpose:");
        log.info("   This tool processes unscraped LinkedIn profiles from the database");
        log.info("   using a rate-limited workflow to avoid API limits.");
        log.info("");
        log.info("⚙️ How it works:");
        log.info("   1. Fetches 1000 unscraped records from the database");
        log.info("   2. Uses 3 concurrent threads to process records");
        log.info("   3. For each record, extracts email and LinkedIn URL");
        log.info("   4. Calls the LinkedIn scraping workflow with rate limiting");
        log.info("   5. Repeats until no more unscraped data is found");
        log.info("");
        log.info("🚦 Rate Limiting & Threading:");
        log.info("   - Maximum 30 requests per minute (1 request per second)");
        log.info("   - 3 concurrent threads for faster processing");
        log.info("   - This prevents API rate limit violations while improving speed");
        log.info("");
        log.info("📝 Commands:");
        log.info("   start  - Begin the batch processing");
        log.info("   status - Show current configuration and status");
        log.info("   help   - Show this help message");
        log.info("   exit   - Exit the application");
        log.info("");
        log.info("🚀 Quick Start:");
        log.info("   java -jar app.jar auto    # Run in automatic mode");
        log.info("   java -jar app.jar         # Run in interactive mode");
        log.info("-".repeat(40));
    }
}
