package ai.beyz.worker.messages.aws.sqs;

import ai.beyz.worker.service.LinkedinSyncMeilisearchService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LinkedinPushSearchListener {

    @Autowired
    private LinkedinSyncMeilisearchService linkedinSyncMeilisearchService;

    //@SqsListener("linkedurl_push_search_queue")
    public void listener(String data) {
        log.info("Received message from linkedurl_push_search_queue: {}", data);
        try {
            linkedinSyncMeilisearchService.syncData(data);
        } catch (Exception e) {
            log.error("❌ Error syncing profile to meilisearch: {}", e.getMessage());
        }
    }
    //@SqsListener("linkedurl_push_search_queue")
    public void listener2(String data) {
        log.info("Received message from linkedurl_push_search_queue-2: {}", data);
        try {
            linkedinSyncMeilisearchService.syncData(data);
        } catch (Exception e) {
            log.error("❌ Error syncing profile to meilisearch: {}", e.getMessage());
        }
    }
    //@SqsListener("linkedurl_push_search_queue")
    public void listener3(String data) {
        log.info("Received message from linkedurl_push_search_queue-3: {}", data);
        try {
            linkedinSyncMeilisearchService.syncData(data);
        } catch (Exception e) {
            log.error("❌ Error syncing profile to meilisearch: {}", e.getMessage());
        }
    }
}
