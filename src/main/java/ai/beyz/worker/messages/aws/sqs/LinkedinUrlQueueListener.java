package ai.beyz.worker.messages.aws.sqs;

import ai.beyz.worker.service.LinkedinScrapeServiceV2;
//import ai.beyz.worker.service.RateLimitedWorkflowService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import lombok.extern.slf4j.Slf4j;
import com.google.common.util.concurrent.RateLimiter;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
//import java.util.concurrent.CompletableFuture;
import com.fasterxml.jackson.databind.ObjectMapper;

//@Component
@Slf4j
public class LinkedinUrlQueueListener {

    @Autowired
    private LinkedinScrapeServiceV2 linkedinScrapeService;

    @Autowired
    RateLimiter rateLimiter;
    //private RateLimitedWorkflowService rateLimitedWorkflowService;
    @Autowired
    private LinkedinScrapeServiceV2 scrapeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SqsTemplate sqsTemplate;

    @SqsListener(value = "linkedin_url_queue",maxConcurrentMessages = "10")
    public void receivedMessage(String data) {
        log.info("📨 Received message from linkedin_url_queue: {}", data);
        try {
            // 关键！在处理之前获取许可。
            // 如果速率过快，这个方法会阻塞当前线程，直到可以获取到许可为止。
            rateLimiter.acquire();

            Map<String, String> map = objectMapper.readValue(data, HashMap.class);
            String email = map.get("email");
            String url = map.get("url");
            scrapeService.workflow(email, url);
        } catch (Exception ex) {
            log.error("❌ Error parsing SQS message (listener-1): {}", ex.getMessage(), ex);
        }

        /*try {
            Map<String, String> map = objectMapper.readValue(data, HashMap.class);
            String email = map.get("email");
            String url = map.get("url");
            
            // 使用限流排队服务异步处理
            CompletableFuture<String> future = rateLimitedWorkflowService.submitWorkflowAsync(email, url);
            
            // 异步处理结果
            future.thenAccept(id -> {
                if (id != null) {
                    log.info("✅ Successfully scraped talent: {} for email: {}", id, email);
                } else {
                    log.warn("⚠️ Workflow completed but returned null for email: {}", email);
                }
            }).exceptionally(throwable -> {
                log.error("❌ Workflow failed for email: {}: {}", email, throwable.getMessage());
                return null;
            });
            
            log.info("📋 Task queued for email: {} (queue status will be logged by workflow service)", email);
            
        } catch (Exception e) {
            log.error("❌ Error parsing SQS message: {}", e.getMessage(), e);
        }*/
    }

    /*@SqsListener("linkedin_url_queue")
    public void receivedMessage2(String data) {
        log.info("📨 Received message from linkedin_url_queue-2: {}", data);
        try {
            Map<String, String> map = objectMapper.readValue(data, HashMap.class);
            String email = map.get("email");
            String url = map.get("url");
            
            // 使用限流排队服务异步处理
            CompletableFuture<String> future = rateLimitedWorkflowService.submitWorkflowAsync(email, url);
            
            // 异步处理结果
            future.thenAccept(id -> {
                if (id != null) {
                    log.info("✅ Successfully scraped talent: {} for email: {} (listener-2)", id, email);
                } else {
                    log.warn("⚠️ Workflow completed but returned null for email: {} (listener-2)", email);
                }
            }).exceptionally(throwable -> {
                log.error("❌ Workflow failed for email: {} (listener-2): {}", email, throwable.getMessage());
                return null;
            });
            
            log.info("📋 Task queued for email: {} (listener-2)", email);
            
        } catch (Exception e) {
            log.error("❌ Error parsing SQS message (listener-2): {}", e.getMessage(), e);
        }
    }*/

}   
