package ai.beyz.worker.job;

import ai.beyz.worker.service.SendDataToQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class QuotaJob {

    @Autowired
    private SendDataToQueueService sendDataToQueueService;

    /**
     * 定时任务
     */
    //@Scheduled(cron = "0 0/10 * * * ?")
    public void execute() {
        sendDataToQueueService.sendDataToQueue(500);
    }
}
