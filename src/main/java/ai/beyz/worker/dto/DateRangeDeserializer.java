package ai.beyz.worker.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * 自定义反序列化器，处理DateRange中的start/end字段可能是字符串或对象的情况
 */
public class DateRangeDeserializer extends JsonDeserializer<LinkedInScrapeResponse.DateRange> {

    @Override
    public LinkedInScrapeResponse.DateRange deserialize(JsonParser parser, DeserializationContext context) 
            throws IOException {
        
        LinkedInScrapeResponse.DateRange dateRange = new LinkedInScrapeResponse.DateRange();
        JsonNode node = parser.getCodec().readTree(parser);
        
        // 处理start字段
        JsonNode startNode = node.get("start");
        if (startNode != null) {
            dateRange.setStart(parseDate(startNode));
        }
        
        // 处理end字段
        JsonNode endNode = node.get("end");
        if (endNode != null) {
            dateRange.setEnd(parseDate(endNode));
            
            // 额外存储对象格式的年月信息
            if (endNode.isObject()) {
                JsonNode yearNode = endNode.get("year");
                JsonNode monthNode = endNode.get("month");
                if (yearNode != null) {
                    dateRange.setEndYear(yearNode.asInt());
                }
                if (monthNode != null) {
                    dateRange.setEndMonth(monthNode.asInt());
                }
            }
        }
        
        return dateRange;
    }
    
    private String parseDate(JsonNode dateNode) {
        if (dateNode == null || dateNode.isNull()) {
            return null;
        }
        
        if (dateNode.isTextual()) {
            // 字符串格式: "2025" 或 "Dec 2025"
            return dateNode.asText();
        } else if (dateNode.isObject()) {
            // 对象格式: {"year": 2025, "month": 12}
            JsonNode yearNode = dateNode.get("year");
            JsonNode monthNode = dateNode.get("month");
            
            if (yearNode != null) {
                int year = yearNode.asInt();
                if (monthNode != null) {
                    int month = monthNode.asInt();
                    // 转换为字符串格式 "Dec 2025"
                    String[] monthNames = {"", "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                                         "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
                    if (month >= 1 && month <= 12) {
                        return monthNames[month] + " " + year;
                    }
                }
                // 只有年份的情况
                return String.valueOf(year);
            }
        }
        
        return null;
    }
} 