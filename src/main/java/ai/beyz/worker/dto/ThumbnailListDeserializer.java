package ai.beyz.worker.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义反序列化器，处理thumbnail字段可能是单个对象或数组的情况
 */
public class ThumbnailListDeserializer extends JsonDeserializer<List<LinkedInScrapeResponse.Thumbnail>> {

    @Override
    public List<LinkedInScrapeResponse.Thumbnail> deserialize(JsonParser parser, DeserializationContext context) 
            throws IOException {
        
        List<LinkedInScrapeResponse.Thumbnail> thumbnails = new ArrayList<>();
        ObjectMapper mapper = (ObjectMapper) parser.getCodec();
        
        JsonToken currentToken = parser.getCurrentToken();
        
        if (currentToken == JsonToken.START_ARRAY) {
            // 如果是数组，正常反序列化
            LinkedInScrapeResponse.Thumbnail[] thumbnailArray = mapper.readValue(parser, LinkedInScrapeResponse.Thumbnail[].class);
            if (thumbnailArray != null) {
                for (LinkedInScrapeResponse.Thumbnail thumbnail : thumbnailArray) {
                    if (thumbnail != null) {
                        thumbnails.add(thumbnail);
                    }
                }
            }
        } else if (currentToken == JsonToken.START_OBJECT) {
            // 如果是单个对象，包装成数组
            LinkedInScrapeResponse.Thumbnail thumbnail = mapper.readValue(parser, LinkedInScrapeResponse.Thumbnail.class);
            if (thumbnail != null) {
                thumbnails.add(thumbnail);
            }
        } else if (currentToken == JsonToken.VALUE_NULL) {
            // 如果是null，返回空列表
            return thumbnails;
        }
        
        return thumbnails;
    }
} 