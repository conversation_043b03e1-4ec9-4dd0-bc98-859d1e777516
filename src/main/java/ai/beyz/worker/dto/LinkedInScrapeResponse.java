package ai.beyz.worker.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LinkedInScrapeResponse {
    private Boolean success;
    private Integer cost;
    private ProfileData data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProfileData {
        private String id;
        private String urn;
        @JsonProperty("public_identifier")
        private String publicIdentifier;
        @JsonProperty("first_name")
        private String firstName;
        @JsonProperty("last_name")
        private String lastName;
        @JsonProperty("full_name")
        private String fullName;
        private String headline;
        @JsonProperty("is_premium")
        private Boolean isPremium;
        @JsonProperty("is_open_to_work")
        private Boolean isOpenToWork;
        @JsonProperty("is_hiring")
        private Boolean isHiring;
        @JsonProperty("is_memorialized")
        private Boolean isMemorialized;
        @JsonProperty("is_influencer")
        private Boolean isInfluencer;
        @JsonProperty("is_top_voice")
        private Boolean isTopVoice;
        @JsonProperty("is_creator")
        private Boolean isCreator;
        private Birth birth;
        private String pronoun;
        private Long created;
        @JsonProperty("created_date")
        private String createdDate;
        private Location location;
        private List<Avatar> avatar;
        private List<Cover> cover;
        @JsonProperty("associated_hashtag")
        private List<String> associatedHashtag;
        private Website website;
        @JsonProperty("supported_locales")
        private List<SupportedLocale> supportedLocales;
        @JsonProperty("primary_locale")
        private PrimaryLocale primaryLocale;
        @JsonProperty("follower_and_connection")
        private FollowerAndConnection followerAndConnection;
        private List<Experience> experiences;
        private List<Skill> skills;
        private List<Certification> certifications;
        private List<Publication> publications;
        private List<Education> educations;
        private List<Honor> honors;
        private List<Volunteer> volunteers;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Birth {
        private Integer day;
        private Integer month;
        private Integer year;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        private String country;
        @JsonProperty("country_code")
        private String countryCode;
        private String city;
        @JsonProperty("geographic_area")
        private String geographicArea;
        private String line1;
        private String line2;
        @JsonProperty("postal_code")
        private String postalCode;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Avatar {
        private Integer width;
        private Integer height;
        private String url;
        @JsonProperty("expires_at")
        private Long expiresAt;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Cover {
        private Integer width;
        private Integer height;
        private String url;
        @JsonProperty("expires_at")
        private Long expiresAt;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Website {
        private String title;
        private String url;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SupportedLocale {
        private String country;
        private String language;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PrimaryLocale {
        private String country;
        private String language;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FollowerAndConnection {
        @JsonProperty("follower_count")
        private Long followerCount;
        @JsonProperty("connection_count")
        private Long connectionCount;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Experience {
        private String title;
        private String description;
        private String location;
        @JsonProperty("employment_type")
        private String employmentType;
        private DateRange date;
        private List<String> skills;
        private Company company;
        private List<Media> media;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = DateRangeDeserializer.class)
    public static class DateRange {
        private String start;
        private String end;
        
        // 额外字段存储对象格式的年月信息（用于精确的日期比较）
        private Integer endYear;
        private Integer endMonth;
        
        /**
         * 检查结束日期是否在未来（支持对象格式的精确比较）
         */
        public boolean isEndInFuture() {
            if (endYear != null) {
                java.time.LocalDateTime now = java.time.LocalDateTime.now();
                int currentYear = now.getYear();
                int currentMonth = now.getMonthValue();
                
                if (endYear > currentYear) {
                    return true;
                } else if (endYear == currentYear && endMonth != null) {
                    return endMonth > currentMonth;
                }
                return false;
            }
            return false;
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Company {
        private String id;
        private String name;
        private String url;
        private List<Avatar> logo;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Media {
        private String title;
        private String description;
        
        @JsonDeserialize(using = ThumbnailListDeserializer.class)
        private List<Thumbnail> thumbnail;  // 使用自定义反序列化器处理单个对象或数组
        
        // 辅助方法：获取第一个缩略图
        public Thumbnail getFirstThumbnail() {
            if (thumbnail != null && !thumbnail.isEmpty()) {
                return thumbnail.get(0);
            }
            return null;
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Thumbnail {
        private Integer width;
        private Integer height;
        private String url;
        @JsonProperty("expires_at")
        private Long expiresAt;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Skill {
        private String skill;
        @JsonProperty("num_endorsements")
        private Integer numEndorsements;
        @JsonProperty("is_passed_skill_assessment")
        private Boolean isPassedSkillAssessment;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Certification {
        private String title;
        private String authority;
        @JsonProperty("credential_url")
        private String credentialUrl;
        @JsonProperty("issued_at")
        private String issuedAt;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Publication {
        private String title;
        private String publication;
        private String description;
        private String date;
        @JsonProperty("publication_url")
        private String publicationUrl;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Education {
        private String school;
        private DateRange date;
        private String degree;
        private String grade;
        private String description;
        private String activities;
        private List<Media> media;
        private List<String> skills;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Honor {
        private String title;
        private String caption;
        @JsonProperty("issued_by")
        private String issuedBy;
        @JsonProperty("issued_at")
        private String issuedAt;
        @JsonProperty("associated_with")
        private String associatedWith;
        private List<Media> media;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Volunteer {
        private String organization;
        private String title;
        private String cause;
        private String duration;
        private String description;
        private DateRange date;
        private List<Media> media;
    }
}
