package ai.beyz.worker.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Cloudflare KV 客户端
 * 基于 Cloudflare KV API 实现键值存储操作
 * 支持单个和多个 namespace 操作
 */
@Slf4j
@Component
public class CloudflareKvClient {

    private final WebClient webClient;
    private final AppProperties appProperties;
    private final ObjectMapper objectMapper;

    @Autowired
    public CloudflareKvClient(WebClient webClient, AppProperties appProperties) {
        this.webClient = webClient;
        this.appProperties = appProperties;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * KV 键值对数据结构
     */
    @Data
    public static class KvPair {
        private String key;
        private String value;
        private Long expiration;
        private Integer expirationTtl;
        private Map<String, Object> metadata;
    }

    /**
     * KV 键信息
     */
    @Data
    public static class KvKey {
        private String name;
        private Long expiration;
        private Map<String, Object> metadata;
    }

    /**
     * 批量操作响应
     */
    @Data
    public static class BulkResponse {
        private int successfulKeyCount;
        private List<String> unsuccessfulKeys;
    }

    // ==================== 默认 namespace 操作方法 ====================

    /**
     * 写入单个键值对（使用默认namespace）
     *
     * @param key 键名
     * @param value 值
     * @return 操作结果
     */
    public Mono<Boolean> put(String key, String value) {
        return put(key, value, null, null, null);
    }

    /**
     * 写入单个键值对（带过期时间，使用默认namespace）
     *
     * @param key 键名
     * @param value 值
     * @param expirationTtl TTL秒数
     * @return 操作结果
     */
    public Mono<Boolean> put(String key, String value, Integer expirationTtl) {
        return put(key, value, null, expirationTtl, null);
    }

    /**
     * 写入单个键值对（完整参数，使用默认namespace）
     *
     * @param key 键名
     * @param value 值
     * @param expiration 绝对过期时间（Unix时间戳）
     * @param expirationTtl TTL秒数
     * @param metadata 元数据
     * @return 操作结果
     */
    public Mono<Boolean> put(String key, String value, Long expiration, Integer expirationTtl, Map<String, Object> metadata) {
        return put(getDefaultNamespaceId(), key, value, expiration, expirationTtl, metadata);
    }

    /**
     * 获取单个键的值（使用默认namespace）
     *
     * @param key 键名
     * @return 值
     */
    public Mono<String> get(String key) {
        return get(getDefaultNamespaceId(), key);
    }

    /**
     * 删除单个键值对（使用默认namespace）
     *
     * @param key 键名
     * @return 操作结果
     */
    public Mono<Boolean> delete(String key) {
        return delete(getDefaultNamespaceId(), key);
    }

    /**
     * 批量写入键值对（使用默认namespace）
     *
     * @param kvPairs 键值对列表（最多10,000个）
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkPut(List<KvPair> kvPairs) {
        return bulkPut(getDefaultNamespaceId(), kvPairs);
    }

    /**
     * 批量获取键值对（使用默认namespace）
     *
     * @param keys 键名列表（最多100个）
     * @return 响应字符串
     */
    public Mono<String> bulkGet(List<String> keys) {
        return bulkGet(getDefaultNamespaceId(), keys, false);
    }

    /**
     * 批量获取键值对（带元数据，使用默认namespace）
     *
     * @param keys 键名列表（最多100个）
     * @param withMetadata 是否包含元数据
     * @return 响应字符串
     */
    public Mono<String> bulkGet(List<String> keys, boolean withMetadata) {
        return bulkGet(getDefaultNamespaceId(), keys, withMetadata);
    }

    /**
     * 批量删除键值对（使用默认namespace）
     *
     * @param keys 键名列表（最多10,000个）
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkDelete(List<String> keys) {
        return bulkDelete(getDefaultNamespaceId(), keys);
    }

    /**
     * 列出命名空间中的所有键（使用默认namespace）
     *
     * @return 响应字符串
     */
    public Mono<String> listKeys() {
        return listKeys(getDefaultNamespaceId(), null, null);
    }

    /**
     * 列出命名空间中的键（分页，使用默认namespace）
     *
     * @param cursor 游标（用于分页）
     * @param limit 限制数量
     * @return 响应字符串
     */
    public Mono<String> listKeys(String cursor, Integer limit) {
        return listKeys(getDefaultNamespaceId(), cursor, limit);
    }

    /**
     * 获取键的元数据（使用默认namespace）
     *
     * @param key 键名
     * @return 元数据响应字符串
     */
    public Mono<String> getMetadata(String key) {
        return getMetadata(getDefaultNamespaceId(), key);
    }

    // ==================== 指定 namespace 操作方法 ====================

    /**
     * 写入单个键值对（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @param value 值
     * @return 操作结果
     */
    public Mono<Boolean> put(String namespaceId, String key, String value) {
        return put(namespaceId, key, value, null, null, null);
    }

    /**
     * 写入单个键值对（带过期时间，指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @param value 值
     * @param expirationTtl TTL秒数
     * @return 操作结果
     */
    public Mono<Boolean> put(String namespaceId, String key, String value, Integer expirationTtl) {
        return put(namespaceId, key, value, null, expirationTtl, null);
    }

    /**
     * 写入单个键值对（完整参数，指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @param value 值
     * @param expiration 绝对过期时间（Unix时间戳）
     * @param expirationTtl TTL秒数
     * @param metadata 元数据
     * @return 操作结果
     */
    public Mono<Boolean> put(String namespaceId, String key, String value, Long expiration, Integer expirationTtl, Map<String, Object> metadata) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.put()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/values/{key}",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId,
                     key)
                .headers(this::addAuthHeaders)
                .contentType(MediaType.TEXT_PLAIN)
                .bodyValue(value)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    log.debug("写入键值对响应: namespace={}, key={}", namespaceId, key);
                    return true;
                })
                .onErrorResume(error -> {
                    log.error("写入键值对失败: namespace={}, key={}", namespaceId, key, error);
                    return Mono.just(false);
                });
    }

    /**
     * 获取单个键的值（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @return 值
     */
    public Mono<String> get(String namespaceId, String key) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.get()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/values/{key}",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId,
                     key)
                .headers(this::addAuthHeaders)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(error -> {
                    log.error("获取键值对失败: namespace={}, key={}", namespaceId, key, error);
                    return Mono.empty();
                });
    }

    /**
     * 安全获取键值（指定namespace，带默认值）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @param defaultValue 默认值
     * @return 值或默认值
     */
    public Mono<String> getWithDefault(String namespaceId, String key, String defaultValue) {
        return get(namespaceId, key)
                .defaultIfEmpty(defaultValue)
                .doOnNext(value -> {
                    if (defaultValue.equals(value)) {
                        log.debug("键不存在，使用默认值: namespace={}, key={}, default={}", namespaceId, key, defaultValue);
                    }
                });
    }

    /**
     * 检查键是否存在（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @return 是否存在
     */
    public Mono<Boolean> exists(String namespaceId, String key) {
        return get(namespaceId, key)
                .map(value -> true)
                .defaultIfEmpty(false)
                .doOnNext(exists -> 
                    log.debug("检查键存在性: namespace={}, key={}, exists={}", namespaceId, key, exists)
                );
    }

    /**
     * 安全获取键值（使用默认namespace，带默认值）
     *
     * @param key 键名
     * @param defaultValue 默认值
     * @return 值或默认值
     */
    public Mono<String> getWithDefault(String key, String defaultValue) {
        return getWithDefault(getDefaultNamespaceId(), key, defaultValue);
    }

    /**
     * 检查键是否存在（使用默认namespace）
     *
     * @param key 键名
     * @return 是否存在
     */
    public Mono<Boolean> exists(String key) {
        return exists(getDefaultNamespaceId(), key);
    }

    /**
     * 删除单个键值对（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @return 操作结果
     */
    public Mono<Boolean> delete(String namespaceId, String key) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.delete()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/values/{key}",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId,
                     key)
                .headers(this::addAuthHeaders)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    log.debug("删除键值对响应: namespace={}, key={}", namespaceId, key);
                    return true;
                })
                .onErrorResume(error -> {
                    log.error("删除键值对失败: namespace={}, key={}", namespaceId, key, error);
                    return Mono.just(false);
                });
    }

    /**
     * 批量写入键值对（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param kvPairs 键值对列表（最多10,000个）
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkPut(String namespaceId, List<KvPair> kvPairs) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.put()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/bulk",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId)
                .headers(this::addAuthHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(kvPairs)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(response);
                        BulkResponse bulkResponse = new BulkResponse();
                        bulkResponse.setSuccessfulKeyCount(jsonNode.path("result").path("successful_key_count").asInt());
                        return bulkResponse;
                    } catch (Exception e) {
                        log.error("解析批量写入响应失败: namespace={}", namespaceId, e);
                        return new BulkResponse();
                    }
                })
                .onErrorResume(error -> {
                    log.error("批量写入键值对失败: namespace={}", namespaceId, error);
                    return Mono.just(new BulkResponse());
                });
    }

    /**
     * 批量获取键值对（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param keys 键名列表（最多100个）
     * @param withMetadata 是否包含元数据
     * @return 响应字符串
     */
    public Mono<String> bulkGet(String namespaceId, List<String> keys, boolean withMetadata) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        Map<String, Object> requestBody = Map.of(
            "keys", keys,
            "withMetadata", withMetadata
        );
        
        return webClient.post()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/bulk/get",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId)
                .headers(this::addAuthHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(error -> {
                    log.error("批量获取键值对失败: namespace={}", namespaceId, error);
                    return Mono.just("{}");
                });
    }

    /**
     * 批量删除键值对（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param keys 键名列表（最多10,000个）
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkDelete(String namespaceId, List<String> keys) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.post()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/bulk/delete",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId)
                .headers(this::addAuthHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(keys)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(response);
                        BulkResponse bulkResponse = new BulkResponse();
                        bulkResponse.setSuccessfulKeyCount(jsonNode.path("result").path("successful_key_count").asInt());
                        return bulkResponse;
                    } catch (Exception e) {
                        log.error("解析批量删除响应失败: namespace={}", namespaceId, e);
                        return new BulkResponse();
                    }
                })
                .onErrorResume(error -> {
                    log.error("批量删除键值对失败: namespace={}", namespaceId, error);
                    return Mono.just(new BulkResponse());
                });
    }

    /**
     * 列出命名空间中的键（分页，指定namespace）
     *
     * @param namespaceId namespace ID
     * @param cursor 游标（用于分页）
     * @param limit 限制数量
     * @return 响应字符串
     */
    public Mono<String> listKeys(String namespaceId, String cursor, Integer limit) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/keys")
                             .build(appProperties.getApi().getCloudflare().getAccountId(),
                                   namespaceId);
                    if (cursor != null) {
                        uriBuilder.queryParam("cursor", cursor);
                    }
                    if (limit != null) {
                        uriBuilder.queryParam("limit", limit);
                    }
                    return uriBuilder.build();
                })
                .headers(this::addAuthHeaders)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(error -> {
                    log.error("获取键列表失败: namespace={}", namespaceId, error);
                    return Mono.just("{}");
                });
    }

    /**
     * 获取键的元数据（指定namespace）
     *
     * @param namespaceId namespace ID
     * @param key 键名
     * @return 元数据响应字符串
     */
    public Mono<String> getMetadata(String namespaceId, String key) {
        String baseUrl = appProperties.getApi().getCloudflare().getBaseUrl();
        
        return webClient.get()
                .uri(baseUrl + "/accounts/{accountId}/storage/kv/namespaces/{namespaceId}/metadata/{key}",
                     appProperties.getApi().getCloudflare().getAccountId(),
                     namespaceId,
                     key)
                .headers(this::addAuthHeaders)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(error -> {
                    log.error("获取元数据失败: namespace={}, key={}", namespaceId, key, error);
                    return Mono.just("{}");
                });
    }

    // ==================== 通过别名操作方法 ====================

    /**
     * 通过namespace别名写入键值对
     *
     * @param namespaceAlias namespace别名
     * @param key 键名
     * @param value 值
     * @return 操作结果
     */
    public Mono<Boolean> putByAlias(String namespaceAlias, String key, String value) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return put(namespaceId, key, value);
    }

    /**
     * 通过namespace别名获取键值
     *
     * @param namespaceAlias namespace别名
     * @param key 键名
     * @return 值
     */
    public Mono<String> getByAlias(String namespaceAlias, String key) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return get(namespaceId, key);
    }

    /**
     * 通过别名安全获取键值（带默认值）
     *
     * @param namespaceAlias namespace别名
     * @param key 键名
     * @param defaultValue 默认值
     * @return 值或默认值
     */
    public Mono<String> getByAliasWithDefault(String namespaceAlias, String key, String defaultValue) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return getWithDefault(namespaceId, key, defaultValue);
    }

    /**
     * 通过别名检查键是否存在
     *
     * @param namespaceAlias namespace别名
     * @param key 键名
     * @return 是否存在
     */
    public Mono<Boolean> existsByAlias(String namespaceAlias, String key) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return exists(namespaceId, key);
    }

    /**
     * 通过namespace别名删除键值对
     *
     * @param namespaceAlias namespace别名
     * @param key 键名
     * @return 操作结果
     */
    public Mono<Boolean> deleteByAlias(String namespaceAlias, String key) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return delete(namespaceId, key);
    }

    /**
     * 通过namespace别名批量写入
     *
     * @param namespaceAlias namespace别名
     * @param kvPairs 键值对列表
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkPutByAlias(String namespaceAlias, List<KvPair> kvPairs) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return bulkPut(namespaceId, kvPairs);
    }

    /**
     * 通过namespace别名批量获取
     *
     * @param namespaceAlias namespace别名
     * @param keys 键名列表
     * @return 响应字符串
     */
    public Mono<String> bulkGetByAlias(String namespaceAlias, List<String> keys) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return bulkGet(namespaceId, keys, false);
    }

    /**
     * 通过namespace别名批量删除
     *
     * @param namespaceAlias namespace别名
     * @param keys 键名列表
     * @return 批量操作结果
     */
    public Mono<BulkResponse> bulkDeleteByAlias(String namespaceAlias, List<String> keys) {
        String namespaceId = getNamespaceIdByAlias(namespaceAlias);
        return bulkDelete(namespaceId, keys);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取默认的namespace ID
     */
    private String getDefaultNamespaceId() {
        return appProperties.getApi().getCloudflare().getKvNamespaceId();
    }

    /**
     * 通过别名获取namespace ID
     *
     * @param alias 别名
     * @return namespace ID
     */
    private String getNamespaceIdByAlias(String alias) {
        Map<String, String> namespaces = appProperties.getApi().getCloudflare().getNamespaces();
        String namespaceId = namespaces.get(alias);
        if (namespaceId == null) {
            throw new IllegalArgumentException("未找到namespace别名: " + alias);
        }
        return namespaceId;
    }

    /**
     * 获取所有配置的namespace
     *
     * @return namespace映射
     */
    public Map<String, String> getAllNamespaces() {
        Map<String, String> allNamespaces = new HashMap<>(appProperties.getApi().getCloudflare().getNamespaces());
        // 添加默认namespace
        if (getDefaultNamespaceId() != null) {
            allNamespaces.put("default", getDefaultNamespaceId());
        }
        return allNamespaces;
    }

    /**
     * 添加认证头部
     */
    private void addAuthHeaders(HttpHeaders headers) {
        headers.setBearerAuth(appProperties.getApi().getCloudflare().getApiToken());
        headers.set("Content-Type", "application/json");
    }
}
