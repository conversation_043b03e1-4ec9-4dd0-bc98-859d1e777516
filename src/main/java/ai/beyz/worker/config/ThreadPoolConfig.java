package ai.beyz.worker.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@Slf4j
public class ThreadPoolConfig {

    /**
     * 批处理LinkedIn爬取专用线程池
     * 固定3个核心线程，最大3个线程
     */
    @Bean(name = "batchLinkedinScrapeExecutor")
    public Executor batchLinkedinScrapeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：3
        executor.setCorePoolSize(3);
        
        // 最大线程数：3 (固定线程池)
        executor.setMaxPoolSize(3);
        
        // 队列容量：100 (避免内存溢出)
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("batch-linkedin-");
        
        // 拒绝策略：调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间：60秒
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(false);
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：30秒
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("🚀 Batch LinkedIn Scrape Thread Pool initialized: core={}, max={}, queue={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
