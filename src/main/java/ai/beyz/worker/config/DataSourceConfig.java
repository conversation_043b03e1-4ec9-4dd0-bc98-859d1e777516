package ai.beyz.worker.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DataSourceConfig {

    @Primary // 标记为主数据源
    @Bean
    @ConfigurationProperties("spring.datasource.profile")
    public DataSourceProperties mainDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean
    public DataSource mainDataSource() {
        return mainDataSourceProperties().initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Primary
    @Bean(name = "mainEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean mainEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(mainDataSource())
                .packages("ai.beyz.worker.entity.profile") // 主数据库实体类所在的包
                .build();
    }

    @Primary
    @Bean
    public PlatformTransactionManager mainTransactionManager(
            @Qualifier("mainEntityManagerFactory") LocalContainerEntityManagerFactoryBean mainEntityManagerFactory) {
        return new JpaTransactionManager(mainEntityManagerFactory.getObject());
    }

    @Configuration
    @EnableJpaRepositories(
            basePackages = "ai.beyz.worker.repository.profile", // 主数据库 Repository 所在的包
            entityManagerFactoryRef = "mainEntityManagerFactory",
            transactionManagerRef = "mainTransactionManager"
    )
    public static class MainJpaRepositoryConfig {}


    //beyz
    @Bean
    @ConfigurationProperties("spring.datasource.beyz")
    public DataSourceProperties beyzDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean
    public DataSource beyzDataSource() {
        return beyzDataSourceProperties().initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }


    @Bean(name = "beyzEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean beyzEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        properties.put("hibernate.implicit_naming_strategy", "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
        properties.put("hibernate.globally_quoted_identifiers", true);
        
        return builder
                .dataSource(beyzDataSource())
                .packages("ai.beyz.worker.entity.beyz") // beyz数据库实体类所在的包
                .persistenceUnit("beyz")
                .properties(properties)
                .build();
    }


    @Bean
    public PlatformTransactionManager beyzTransactionManager(
            @Qualifier("beyzEntityManagerFactory") LocalContainerEntityManagerFactoryBean beyzEntityManagerFactory) {
        return new JpaTransactionManager(beyzEntityManagerFactory.getObject());
    }

    @Configuration
    @EnableJpaRepositories(
            basePackages = "ai.beyz.worker.repository.beyz", // beyz数据库 Repository 所在的包
            entityManagerFactoryRef = "beyzEntityManagerFactory",
            transactionManagerRef = "beyzTransactionManager"
    )
    public static class BeyzJpaRepositoryConfig {}
}
