package ai.beyz.worker.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;
import java.util.Map;
import java.util.HashMap;

@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppProperties {
    private Api api;

    @Data
    public static class Api {
        private EmailVerifier emailVerifier;
        private LinkedinScraper linkedinScraper;
        private Openai openai;
        private Google google;
        private Meilisearch meilisearch;
        private Cloudflare cloudflare;
    }

    @Data
    public static class EmailVerifier {
        private String url;
    }

    @Data
    public static class LinkedinScraper {
        private String url;
        private String key;
        private String host;
    }
    
    @Data
    public static class Google {
        private String key;
        private String model;
    }

    @Data
    public static class Meilisearch {
        private String url;
        private String key;
        private String index;
    }

    @Data
    public static class Openai {
        private String key;
        private String model;
    }
    
    @Data
    public static class Cloudflare {
        private String accountId;
        private String apiToken;
        private String kvNamespaceId;
        private String baseUrl = "https://api.cloudflare.com/client/v4";
        private Map<String, String> namespaces = new HashMap<>();
    }
}
