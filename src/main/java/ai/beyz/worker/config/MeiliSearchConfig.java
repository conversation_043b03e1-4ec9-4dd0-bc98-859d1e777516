package ai.beyz.worker.config;

import com.meilisearch.sdk.Client;
import com.meilisearch.sdk.Config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;

@Configuration
public class MeiliSearchConfig {

    @Value("${app.meilisearch.url}")
    private String url;
    @Value("${app.meilisearch.key}")
    private String key;

    @Bean
    public Client meilisearchClient() {
        return new Client(new Config(url, key));
    }

}
