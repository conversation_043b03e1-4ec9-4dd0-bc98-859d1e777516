package ai.beyz.worker.config;

import com.pgvector.PGvector;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class VectorConverter implements AttributeConverter<float[], PGvector> {

    @Override
    public PGvector convertToDatabaseColumn(float[] attribute) {
        // 当实体中的属性为 null 时，直接返回 null
        if (attribute == null) {
            return null;
        }
        // 将 float[] 转换为 PGvector 对象
        return new PGvector(attribute);
    }

    @Override
    public float[] convertToEntityAttribute(PGvector dbData) {
        // 当数据库中的值为 null 时，直接返回 null
        if (dbData == null) {
            return null;
        }
        // 将 PGvector 对象转换为 float[]
        return dbData.toArray();
    }
}