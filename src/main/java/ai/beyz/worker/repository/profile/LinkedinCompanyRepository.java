package ai.beyz.worker.repository.profile;

import ai.beyz.worker.entity.profile.LinkedinCompany;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.Optional;
import java.util.List;

@Repository
public interface LinkedinCompanyRepository extends JpaRepository<LinkedinCompany, String> {

    /**
     * 根据公司名称查找公司
     */
    Optional<LinkedinCompany> findByName(String name);

    /**
     * 根据通用名称查找公司
     */
    Optional<LinkedinCompany> findByUniversalName(String universalName);

    /**
     * 根据公司名称模糊查询
     */
    @Query("SELECT c FROM LinkedinCompany c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<LinkedinCompany> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * 根据行业查找公司
     */
    List<LinkedinCompany> findByIndustry(String industry);

    /**
     * 检查公司是否存在（根据名称）
     */
    boolean existsByName(String name);

    /**
     * 根据公司规模查找公司
     */
    List<LinkedinCompany> findByCompanySize(String companySize);

    /**
     * 根据公司ID查找公司
     */
    Optional<LinkedinCompany> findByCompanyId(String companyId);
} 