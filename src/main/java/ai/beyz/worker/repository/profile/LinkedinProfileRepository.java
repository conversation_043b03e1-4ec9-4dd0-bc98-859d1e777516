package ai.beyz.worker.repository.profile;

import ai.beyz.worker.entity.profile.LinkedinProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import java.util.Set;

@Repository
public interface LinkedinProfileRepository extends JpaRepository<LinkedinProfile,String> {

    // 高效地获取所有已存在的 public_identifier，用于去重
    @Query("SELECT lp.publicIdentifier FROM LinkedinProfile lp WHERE lp.publicIdentifier IS NOT NULL")
    Set<String> findAllPublicIdentifiers();
}
