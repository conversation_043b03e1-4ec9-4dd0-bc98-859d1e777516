package ai.beyz.worker.repository.beyz;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import ai.beyz.worker.entity.beyz.MailingList;

@Repository
public interface MailingListRepository extends JpaRepository<MailingList, String> {

    @Query("SELECT m FROM MailingList m WHERE m.email = :email")
    MailingList findByEmail(@Param("email") String email);
    
    @Query("SELECT m FROM MailingList m WHERE m.scraped = false AND m.email IS NOT NULL AND m.email != '' AND m.linkedinUrl IS NOT NULL AND m.linkedinUrl != '' AND m.status = 0 AND m.linkedinUrl LIKE 'https://www.linkedin.com/in/%' ORDER BY m.email LIMIT :limit")
    List<MailingList> findUnscrapedWithEmailAndLinkedinUrl(@Param("limit") int limit);

    MailingList findByEmailAndLinkedinUrl(String email, String linkedinUrl);
}
