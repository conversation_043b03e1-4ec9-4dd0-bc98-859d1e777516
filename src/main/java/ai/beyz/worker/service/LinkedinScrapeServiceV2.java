package ai.beyz.worker.service;

import ai.beyz.worker.config.AppProperties;
import ai.beyz.worker.config.CloudflareKvClient;
import ai.beyz.worker.dto.EmailValidationResponse;
import ai.beyz.worker.dto.LinkedInScrapeResponse;
import ai.beyz.worker.entity.beyz.MailingList;
import ai.beyz.worker.entity.profile.LinkedinProfile;
import ai.beyz.worker.entity.profile.Talent;
import ai.beyz.worker.entity.profile.TalentDetail;
import ai.beyz.worker.repository.beyz.MailingListRepository;
import ai.beyz.worker.repository.profile.LinkedinProfileRepository;
import ai.beyz.worker.repository.profile.TalentRepository;
import ai.beyz.worker.repository.profile.TalentDetailRepository;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.filter.ValueFilter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class LinkedinScrapeServiceV2 {

    @Autowired
    private WebClient webClient;

    @Autowired
    private AppProperties props;

    @Autowired
    private LinkedinProfileRepository linkedinProfileRepository;

    @Autowired
    private ProfileEnrichmentService profileEnrichmentService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MailingListRepository mailingListRepository;

    @Autowired
    private CloudflareKvClient cloudflareKvClient;

    @Autowired
    private OpenAiEmbeddingModel embeddingModel;

    @Autowired
    private TalentRepository talentRepository;

    @Autowired
    private TalentDetailRepository talentDetailRepository;


    /**
     * 完整的工作流程 - 验证邮箱、爬取数据、转换并保存
     */
    public String workflow(String email, String url) {
        log.debug("🚀 Starting LinkedIn scraping workflow for email: {}, url: {}", email, url);

        // 1. 验证邮箱地址有效性
        if (!validateEmail(email)) {

            log.warn("❌ Invalid email address: {}", email);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("Invalid email address");
                mailingListRepository.save(mailingList);
            }
            return null;
        }

        // 2. 验证是否是印度人
        LinkedInScrapeResponse.ProfileData profileData = scrapeLinkedinProfileValidateIndia(url);
        if (profileData == null) {
            log.warn("❌ Failed to scrape LinkedIn profile for {}", url);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("Failed to scrape LinkedIn profile");
                mailingListRepository.save(mailingList);
            }
            return null;
        }
        if ("India".equalsIgnoreCase( profileData.getLocation().getCountry())) {
            log.warn("❌ The profile is an Indian: {}", url);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("The candidate is Indian.");
                mailingListRepository.save(mailingList);
            }
            return null;
        }


        // 3. 根据LinkedIn URL获取profile数据
        profileData = scrapeLinkedinProfile(url);

        // 3. 解析profile数据
        String id = convertToTalent(profileData, email, url);

        // 5. 更新MailingList表
        if (id != null) {
            MailingList mailingList = mailingListRepository.findByEmail(email);
            if (mailingList != null) {
                mailingList.setScraped(true);
                mailingListRepository.save(mailingList);
            }
            log.info("🔍 Saved profile: {}", id);
            return id;
        } else {
            return null;
        }

    }





    /**
     * 验证邮箱地址有效性
     */
    public boolean validateEmail(String email) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("Validating email: {}", email);
            EmailValidationResponse response = webClient.get()
                    .uri(props.getApi().getEmailVerifier().getUrl(), email)
                    .retrieve()
                    .bodyToMono(EmailValidationResponse.class)
                    .timeout(Duration.ofSeconds(10)) // 设置10秒超时
                    .block(); // 同步等待结果
            long duration = System.currentTimeMillis() - startTime;
            log.debug("⏱️ Email validation for {} took {}ms", email, duration);
            return response != null && "ok".equals(response.getResult());
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Error validating email {} after {}ms: {}", email, duration, e.getMessage());
            return false;
        }
    }

    /**
     * 验证是否是印度人
     * @return
     */
    @Retryable(value = { Exception.class }, // 仅在网络连接相关异常时重试
            maxAttempts = 3, backoff = @Backoff(delay = 2000, multiplier = 2) // 首次延迟2秒，后续延迟时间加倍
    )
    public LinkedInScrapeResponse.ProfileData scrapeLinkedinProfileValidateIndia(String url) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("Scraping linkedin profile for {}", url);
            // 如果缓存不存在,则调用API获取结果并存储到Cloudflare KV
            String username = extractUsernameFromUrl(url);
            if (username == null) {
                log.warn("Could not extract username from URL: {}", url);
                return null;
            }
            log.debug("Extracted username: {}", username);

            // 调用LinkedIn API获取profile数据
            LinkedInScrapeResponse response = webClient.get()
                    .uri(props.getApi().getLinkedinScraper().getUrl(), uriBuilder -> uriBuilder
                            .queryParam("username", username)
                            .queryParam("_admin",true)
                            .build())
                    .header("x-rapidapi-key", props.getApi().getLinkedinScraper().getKey())
                    .header("x-rapidapi-host", props.getApi().getLinkedinScraper().getHost())
                    .retrieve()
                    // 如果HTTP状态码是4xx或5xx, retrieve()会抛出WebClientResponseException
                    // 我们捕获它，记录日志并返回一个空的Mono，这样就不会触发重试
                    .onStatus(HttpStatusCode::isError, clientResponse -> {
                        log.error("❌ Failed to scrape LinkedIn for {}: HTTP {}",
                                username, clientResponse.statusCode().value());
                        //return Mono.empty(); // 返回空，表示处理完成，不要抛异常
                        return clientResponse.createException().flatMap(Mono::error);
                    })
                    .bodyToMono(LinkedInScrapeResponse.class)
                    .timeout(Duration.ofSeconds(30)) // 设置30秒超时
                    .block(); // 同步等待

            if (response == null) {
                log.warn("❌ No response from LinkedIn for {}", username);
                return null;
            }

            if (response.getSuccess() != null && response.getSuccess()) {
                log.debug("✅ Successfully scraped LinkedIn profile for {}", username);
                log.info("💰 cost: {}", response.getCost());
                return response.getData();
            } else {
                log.warn("❌ LinkedIn API returned unsuccessful response for {}", username);
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error scraping linkedin profile for {}: {}", url, e.getMessage());
            e.printStackTrace();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("⏱️ Scraping linkedin profile for {} took {}ms", url, duration);
        }
        return null;
    }

    /**
     * 根据LinkedIn URL获取profile数据
     * @return
     */
    @Retryable(value = { Exception.class }, // 仅在网络连接相关异常时重试
            maxAttempts = 3, backoff = @Backoff(delay = 2000, multiplier = 2) // 首次延迟2秒，后续延迟时间加倍
    )
    public LinkedInScrapeResponse.ProfileData scrapeLinkedinProfile(String url) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("Scraping linkedin profile for {}", url);
            // 如果缓存不存在,则调用API获取结果并存储到Cloudflare KV
            String username = extractUsernameFromUrl(url);
            if (username == null) {
                log.warn("Could not extract username from URL: {}", url);
                return null;
            }
            log.debug("Extracted username: {}", username);

            // 调用LinkedIn API获取profile数据
            LinkedInScrapeResponse response = webClient.get()
                    .uri(props.getApi().getLinkedinScraper().getUrl(), uriBuilder -> uriBuilder
                            .queryParam("username", username)
                            .queryParam("include_experiences", "true")
                            .queryParam("include_skills", "true")
                            .queryParam("include_educations", "true")
                            .queryParam("_admin",true)
                            .build())
                    .header("x-rapidapi-key", props.getApi().getLinkedinScraper().getKey())
                    .header("x-rapidapi-host", props.getApi().getLinkedinScraper().getHost())
                    .retrieve()
                    // 如果HTTP状态码是4xx或5xx, retrieve()会抛出WebClientResponseException
                    // 我们捕获它，记录日志并返回一个空的Mono，这样就不会触发重试
                    .onStatus(HttpStatusCode::isError, clientResponse -> {
                        log.error("❌ Failed to scrape LinkedIn for {}: HTTP {}",
                                username, clientResponse.statusCode().value());
                        //return Mono.empty(); // 返回空，表示处理完成，不要抛异常
                        return clientResponse.createException().flatMap(Mono::error);
                    })
                    .bodyToMono(LinkedInScrapeResponse.class)
                    .timeout(Duration.ofSeconds(30)) // 设置30秒超时
                    .block(); // 同步等待
            
            if (response == null) {
                log.warn("❌ No response from LinkedIn for {}", username);
                return null;
            }

            if (response.getSuccess() != null && response.getSuccess()) {
                log.debug("✅ Successfully scraped LinkedIn profile for {}", username);
                log.info("💰 cost: {}", response.getCost());
                return response.getData();
            } else {
                log.warn("❌ LinkedIn API returned unsuccessful response for {}", username);
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error scraping linkedin profile for {}: {}", url, e.getMessage());
            e.printStackTrace();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("⏱️ Scraping linkedin profile for {} took {}ms", url, duration);
        }
        return null;
    }


    /**
     * 将API返回的ProfileData转换为Talent和TalentDetail实体并保存
     */
    private String convertToTalent(LinkedInScrapeResponse.ProfileData profileData, String email, String linkedinUrl) {
        if (profileData == null) {
            log.warn("❌ ProfileData is null, cannot convert to Talent");
            return null;
        }

        try {
            // 1. 创建并保存 Talent 实体
            Talent talent = createTalentFromProfileData(profileData, email, linkedinUrl);
            Talent savedTalent = talentRepository.save(talent);
            log.debug("✅ Successfully saved Talent with ID: {}", savedTalent.getId());

            // 2. 创建并保存 TalentDetail 实体
            TalentDetail talentDetail = createTalentDetailFromProfileData(profileData, savedTalent.getId());
            TalentDetail savedTalentDetail = talentDetailRepository.save(talentDetail);
            log.debug("✅ Successfully saved TalentDetail for Talent ID: {}", savedTalentDetail.getTalentId());

            return savedTalent.getId();

        } catch (Exception e) {
            log.error("❌ Error converting ProfileData to Talent/TalentDetail: {}", e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从ProfileData创建Talent实体
     */
    private Talent createTalentFromProfileData(LinkedInScrapeResponse.ProfileData profileData, String email, String linkedinUrl) {
        Talent talent = new Talent();

        // 基本信息 - 过滤所有字符串字段中的 \u0000 字符
        talent.setId(profileData.getId() != null ? profileData.getId() : UUID.randomUUID().toString());
        talent.setUrn(profileData.getUrn() != null ? profileData.getUrn().replace("\u0000", "") : null);
        talent.setPublicIdentifier(profileData.getPublicIdentifier() != null ? profileData.getPublicIdentifier().replace("\u0000", "") : null);

        // 设置用户头像（取第一个头像URL）
        if (profileData.getAvatar() != null && !profileData.getAvatar().isEmpty()) {
            String avatarUrl = profileData.getAvatar().get(0).getUrl();
            talent.setUserAvatar(avatarUrl != null ? avatarUrl.replace("\u0000", "") : null);
        }

        talent.setFirstName(profileData.getFirstName() != null ? profileData.getFirstName().replace("\u0000", "") : null);
        talent.setLastName(profileData.getLastName() != null ? profileData.getLastName().replace("\u0000", "") : null);
        talent.setFullName(profileData.getFullName() != null ? profileData.getFullName().replace("\u0000", "") : null);
        talent.setHeadline(profileData.getHeadline() != null ? profileData.getHeadline().replace("\u0000", "") : null);

        // 状态字段
        talent.setIsPremium(profileData.getIsPremium());
        talent.setIsOpenToWork(profileData.getIsOpenToWork());
        talent.setIsHiring(profileData.getIsHiring());
        talent.setIsMemorialized(profileData.getIsMemorialized());
        talent.setIsInfluencer(profileData.getIsInfluencer());
        talent.setIsTopVoice(profileData.getIsTopVoice());
        talent.setIsCreator(profileData.getIsCreator());

        // 出生信息
        if (profileData.getBirth() != null) {
            talent.setBirthDay(profileData.getBirth().getDay());
            talent.setBirthMonth(profileData.getBirth().getMonth());
            talent.setBirthYear(profileData.getBirth().getYear());
        }

        // 其他基本信息 - 过滤字符串字段
        talent.setPronoun(profileData.getPronoun() != null ? profileData.getPronoun().replace("\u0000", "") : null);
        talent.setCreated(profileData.getCreated());

        // 解析创建日期
        if (profileData.getCreatedDate() != null) {
            try {
                talent.setCreatedDate(LocalDateTime.parse(profileData.getCreatedDate(),
                        DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                log.warn("Failed to parse created_date: {}", profileData.getCreatedDate());
            }
        }

        // 地址信息 - 过滤字符串字段
        if (profileData.getLocation() != null) {
            talent.setLocationCountry(profileData.getLocation().getCountry() != null ?
                profileData.getLocation().getCountry().replace("\u0000", "") : null);
            talent.setLocationCountryCode(profileData.getLocation().getCountryCode() != null ?
                profileData.getLocation().getCountryCode().replace("\u0000", "") : null);
            talent.setLocationCity(profileData.getLocation().getCity() != null ?
                profileData.getLocation().getCity().replace("\u0000", "") : null);
        }

        // 网站信息 - 过滤字符串字段
        if (profileData.getWebsite() != null) {
            talent.setWebsiteTitle(profileData.getWebsite().getTitle() != null ?
                profileData.getWebsite().getTitle().replace("\u0000", "") : null);
            talent.setWebsiteUrl(profileData.getWebsite().getUrl() != null ?
                profileData.getWebsite().getUrl().replace("\u0000", "") : null);
        }

        // 主要语言环境 - 过滤字符串字段
        if (profileData.getPrimaryLocale() != null) {
            talent.setPrimaryLocaleCountry(profileData.getPrimaryLocale().getCountry() != null ?
                profileData.getPrimaryLocale().getCountry().replace("\u0000", "") : null);
            talent.setPrimaryLocaleLanguage(profileData.getPrimaryLocale().getLanguage() != null ?
                profileData.getPrimaryLocale().getLanguage().replace("\u0000", "") : null);
        }

        // 关注者和连接数
        if (profileData.getFollowerAndConnection() != null) {
            talent.setFollowerCount(profileData.getFollowerAndConnection().getFollowerCount());
            talent.setConnectionCount(profileData.getFollowerAndConnection().getConnectionCount());
        }

        // 外部提供的信息
        talent.setEmail(email);
        talent.setLinkedinUrl(linkedinUrl);

        // 设置时间戳
        talent.setUpdatedDate(LocalDateTime.now());
        talent.setEnriched(false); // 默认未丰富化

        // 提取当前职位信息
        /*if (profileData.getExperiences() != null && !profileData.getExperiences().isEmpty()) {
            LinkedInScrapeResponse.Experience currentExp = profileData.getExperiences().get(0);
            if (currentExp != null) {
                talent.setCurrentTitle(currentExp.getTitle());
                if (currentExp.getCompany() != null) {
                    talent.setCurrentCompany(currentExp.getCompany().getName());
                }
            }
        }*/

        // 提取学历信息 - 过滤字符串字段
        if (profileData.getEducations() != null && !profileData.getEducations().isEmpty()) {
            LinkedInScrapeResponse.Education education = profileData.getEducations().get(0);
            if (education != null) {
                talent.setHighestDegree(education.getDegree() != null ?
                    education.getDegree().replace("\u0000", "") : null);
                talent.setFieldOfStudy(education.getDescription() != null ?
                    education.getDescription().replace("\u0000", "") : null);
                // 提取毕业年份
                if (education.getDate() != null && education.getDate().getEnd() != null) {
                    try {
                        // 尝试从日期字符串中提取年份
                        String endDate = education.getDate().getEnd().replace("\u0000", "");
                        // 如果end是数字格式的年份
                        if (endDate.matches("\\d{4}")) {
                            talent.setGraduationYear(Integer.parseInt(endDate));
                        }
                        // 如果有endYear字段，优先使用
                        else if (education.getDate().getEndYear() != null) {
                            talent.setGraduationYear(education.getDate().getEndYear());
                        }
                    } catch (Exception e) {
                        log.warn("Failed to extract graduation year from end date: {}", education.getDate().getEnd());
                    }
                }
            }
        }

        return talent;
    }

    /**
     * 从ProfileData创建TalentDetail实体
     */
    private TalentDetail createTalentDetailFromProfileData(LinkedInScrapeResponse.ProfileData profileData, String talentId) {
        TalentDetail talentDetail = new TalentDetail();

        // 设置关联的talent ID
        talentDetail.setTalentId(talentId);

        // 详细信息（序列化为JSON）- 定义过滤器来移除 \u0000 字符
        ValueFilter nullCharFilter = (object, name, value) -> {
            // 检查值的类型是否为 String
            if (value instanceof String) {
                // 如果是，则替换掉 \u0000 字符
                return ((String) value).replace("\u0000", "");
            }
            // 如果不是 String 类型，则返回原值
            return value;
        };

        // 序列化复杂对象为JSON字符串 - 所有序列化都使用过滤器
        talentDetail.setAvatar(JSON.toJSONString(profileData.getAvatar(), nullCharFilter));
        talentDetail.setCover(JSON.toJSONString(profileData.getCover(), nullCharFilter));
        talentDetail.setSupportedLocales(JSON.toJSONString(profileData.getSupportedLocales(), nullCharFilter));

        // 处理数组字段 - associatedHashtag
        if (profileData.getAssociatedHashtag() != null) {
            // 对数组中的每个字符串也进行过滤
            String[] filteredHashtags = profileData.getAssociatedHashtag().stream()
                    .map(hashtag -> hashtag != null ? hashtag.replace("\u0000", "") : hashtag)
                    .toArray(String[]::new);
            talentDetail.setAssociatedHashtag(filteredHashtags);
        }

        // 详细信息（序列化为JSON）- 所有字段都使用过滤器
        talentDetail.setExperiences(JSON.toJSONString(profileData.getExperiences(), nullCharFilter));
        talentDetail.setSkills(JSON.toJSONString(profileData.getSkills(), nullCharFilter));
        talentDetail.setCertifications(JSON.toJSONString(profileData.getCertifications(), nullCharFilter));
        talentDetail.setPublications(JSON.toJSONString(profileData.getPublications(), nullCharFilter));
        talentDetail.setEducations(JSON.toJSONString(profileData.getEducations(), nullCharFilter));
        talentDetail.setHonors(JSON.toJSONString(profileData.getHonors(), nullCharFilter));
        talentDetail.setVolunteers(JSON.toJSONString(profileData.getVolunteers(), nullCharFilter));

        // 生成公司列表和学校列表 - 修复过滤器使用方式
        talentDetail.setCompanyList(JSON.toJSONString(
                profileData.getExperiences() != null ? profileData.getExperiences().stream()
                        .filter(exp -> exp.getCompany() != null)
                        .map(exp -> exp.getCompany().getName())
                        .filter(name -> name != null)
                        .map(name -> name.replace("\u0000", "")) // 直接过滤字符串
                        .distinct()
                        .toList() : null, nullCharFilter));

        talentDetail.setSchoolList(JSON.toJSONString(
                profileData.getEducations() != null ? profileData.getEducations().stream()
                        .map(LinkedInScrapeResponse.Education::getSchool)
                        .filter(school -> school != null)
                        .map(school -> school.replace("\u0000", "")) // 直接过滤字符串
                        .distinct()
                        .toList() : null, nullCharFilter));

        // 设置摘要（如果存在）
        // 注意：这里可能需要根据实际的ProfileData结构调整
        // talentDetail.setSummary(profileData.getSummary());
        talentDetail.setOriginText(JSON.toJSONString(profileData, nullCharFilter));

        return talentDetail;
    }


    private String extractUsernameFromUrl(String url) {
        try {
            String path = url.replaceFirst("^(http[s]?://)?(www\\.)?linkedin\\.com/in/", "");
            return path.split("/")[0];
        } catch (Exception e) {
            log.warn("Could not extract username from URL: {}", url);
            return null;
        }
    }

    /**
     * 将对象序列化为JSON字符串
     */
    private String serializeToJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize object to JSON: {}", e.getMessage());
            return null;
        }
    }
}
