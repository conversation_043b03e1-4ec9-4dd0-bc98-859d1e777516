package ai.beyz.worker.service;

import ai.beyz.worker.config.CloudflareKvClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * Cloudflare KV 服务
 * 封装KV操作的业务逻辑，支持多namespace操作
 */
@Slf4j
@Service
public class CloudflareKvService {

    private final CloudflareKvClient kvClient;

    @Autowired
    public CloudflareKvService(CloudflareKvClient kvClient) {
        this.kvClient = kvClient;
    }

    // ==================== 默认namespace操作 ====================

    /**
     * 存储用户配额信息（使用默认namespace）
     *
     * @param userId 用户ID
     * @param quota 配额值
     * @return 操作结果
     */
    public Mono<Boolean> storeUserQuota(String userId, int quota) {
        String key = "user_quota:" + userId;
        String value = String.valueOf(quota);
        
        // 设置7天过期
        int ttl = 7 * 24 * 60 * 60; // 7天
        
        return kvClient.put(key, value, ttl)
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("用户配额存储成功: userId={}, quota={}", userId, quota);
                    } else {
                        log.error("用户配额存储失败: userId={}, quota={}", userId, quota);
                    }
                });
    }

    /**
     * 获取用户配额信息（使用默认namespace）
     *
     * @param userId 用户ID
     * @return 配额值
     */
    public Mono<Integer> getUserQuota(String userId) {
        String key = "user_quota:" + userId;
        
        return kvClient.get(key)
                .map(value -> {
                    try {
                        return Integer.parseInt(value);
                    } catch (NumberFormatException e) {
                        log.warn("配额值格式错误: userId={}, value={}", userId, value);
                        return 0;
                    }
                })
                .defaultIfEmpty(0)
                .doOnNext(quota -> log.debug("获取用户配额: userId={}, quota={}", userId, quota));
    }

    // ==================== 缓存namespace操作 ====================

    /**
     * 存储缓存数据（使用cache namespace）
     *
     * @param key 缓存键
     * @param data 缓存数据
     * @param ttlSeconds TTL秒数
     * @return 操作结果
     */
    public Mono<Boolean> cacheData(String key, String data, Integer ttlSeconds) {
        return kvClient.putByAlias("cache", key, data)
                .doOnSuccess(success -> {
                    if (success) {
                        log.debug("缓存数据存储成功: key={}", key);
                    } else {
                        log.error("缓存数据存储失败: key={}", key);
                    }
                });
    }

    /**
     * 获取缓存数据（使用cache namespace）
     *
     * @param key 缓存键
     * @return 缓存数据
     */
    public Mono<String> getCachedData(String key) {
        return kvClient.getByAlias("cache", key)
                .doOnNext(data -> log.debug("获取缓存数据: key={}", key))
                .doOnError(error -> log.error("获取缓存数据失败: key={}", key, error));
    }

    /**
     * 删除缓存数据（使用cache namespace）
     *
     * @param key 缓存键
     * @return 操作结果
     */
    public Mono<Boolean> deleteCachedData(String key) {
        return kvClient.deleteByAlias("cache", key)
                .doOnSuccess(success -> {
                    if (success) {
                        log.debug("删除缓存数据成功: key={}", key);
                    } else {
                        log.error("删除缓存数据失败: key={}", key);
                    }
                });
    }

    // ==================== 会话namespace操作 ====================

    /**
     * 存储用户会话（使用session namespace）
     *
     * @param sessionId 会话ID
     * @param sessionData 会话数据
     * @return 操作结果
     */
    public Mono<Boolean> storeUserSession(String sessionId, String sessionData) {
        // 会话过期时间：30分钟
        int sessionTtl = 30 * 60;
        
        return kvClient.putByAlias("session", sessionId, sessionData)
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("用户会话存储成功: sessionId={}", sessionId);
                    } else {
                        log.error("用户会话存储失败: sessionId={}", sessionId);
                    }
                });
    }

    /**
     * 获取用户会话（使用session namespace）
     *
     * @param sessionId 会话ID
     * @return 会话数据
     */
    public Mono<String> getUserSession(String sessionId) {
        return kvClient.getByAlias("session", sessionId)
                .doOnNext(data -> log.debug("获取用户会话: sessionId={}", sessionId))
                .doOnError(error -> log.error("获取用户会话失败: sessionId={}", sessionId, error));
    }

    /**
     * 删除用户会话（使用session namespace）
     *
     * @param sessionId 会话ID
     * @return 操作结果
     */
    public Mono<Boolean> deleteUserSession(String sessionId) {
        return kvClient.deleteByAlias("session", sessionId)
                .doOnSuccess(success -> log.info("删除用户会话: sessionId={}, 成功={}", sessionId, success));
    }

    // ==================== 用户数据namespace操作 ====================

    /**
     * 批量存储用户数据（使用user-data namespace）
     *
     * @param userDataMap 用户数据映射
     * @return 批量操作结果
     */
    public Mono<CloudflareKvClient.BulkResponse> batchStoreUserData(Map<String, String> userDataMap) {
        List<CloudflareKvClient.KvPair> kvPairs = userDataMap.entrySet().stream()
                .map(entry -> {
                    CloudflareKvClient.KvPair kvPair = new CloudflareKvClient.KvPair();
                    kvPair.setKey("user_profile:" + entry.getKey());
                    kvPair.setValue(entry.getValue());
                    kvPair.setExpirationTtl(24 * 60 * 60); // 1天过期
                    return kvPair;
                })
                .toList();

        return kvClient.bulkPutByAlias("user-data", kvPairs)
                .doOnSuccess(response -> 
                    log.info("批量存储用户数据完成: 成功数量={}", response.getSuccessfulKeyCount())
                );
    }

    /**
     * 批量获取用户数据（使用user-data namespace）
     *
     * @param userIds 用户ID列表
     * @return 批量获取响应
     */
    public Mono<String> batchGetUserData(List<String> userIds) {
        List<String> keys = userIds.stream()
                .map(userId -> "user_profile:" + userId)
                .toList();

        return kvClient.bulkGetByAlias("user-data", keys)
                .doOnSuccess(response -> log.info("批量获取用户数据完成"));
    }

    /**
     * 批量删除用户数据（使用user-data namespace）
     *
     * @param userIds 用户ID列表
     * @return 批量操作结果
     */
    public Mono<CloudflareKvClient.BulkResponse> batchDeleteUserData(List<String> userIds) {
        List<String> keys = userIds.stream()
                .map(userId -> "user_profile:" + userId)
                .toList();

        return kvClient.bulkDeleteByAlias("user-data", keys)
                .doOnSuccess(response -> 
                    log.info("批量删除用户数据完成: 成功数量={}", response.getSuccessfulKeyCount())
                );
    }

    // ==================== 分析数据namespace操作 ====================

    /**
     * 存储分析数据（使用analytics namespace）
     *
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return 操作结果
     */
    public Mono<Boolean> storeAnalyticsData(String eventType, String eventData) {
        String key = "analytics:" + eventType + ":" + System.currentTimeMillis();
        
        // 分析数据保存30天
        int analyticsTtl = 30 * 24 * 60 * 60;
        
        return kvClient.putByAlias("analytics", key, eventData)
                .doOnSuccess(success -> {
                    if (success) {
                        log.debug("分析数据存储成功: eventType={}", eventType);
                    } else {
                        log.error("分析数据存储失败: eventType={}", eventType);
                    }
                });
    }

    /**
     * 获取分析数据列表（使用analytics namespace）
     *
     * @return 分析数据键列表
     */
    public Mono<String> getAnalyticsDataKeys() {
        return kvClient.listKeys("analytics-namespace-id", null, 100)
                .doOnSuccess(response -> log.info("获取分析数据键列表完成"));
    }

    // ==================== 临时数据namespace操作 ====================

    /**
     * 存储临时数据（使用temp namespace）
     *
     * @param key 键名
     * @param data 数据
     * @param ttlSeconds TTL秒数
     * @return 操作结果
     */
    public Mono<Boolean> storeTempData(String key, String data, Integer ttlSeconds) {
        return kvClient.putByAlias("temp", key, data)
                .doOnSuccess(success -> {
                    if (success) {
                        log.debug("临时数据存储成功: key={}, ttl={}s", key, ttlSeconds);
                    } else {
                        log.error("临时数据存储失败: key={}", key);
                    }
                });
    }

    /**
     * 获取临时数据（使用temp namespace）
     *
     * @param key 键名
     * @return 数据
     */
    public Mono<String> getTempData(String key) {
        return kvClient.getByAlias("temp", key)
                .doOnNext(data -> log.debug("获取临时数据: key={}", key))
                .doOnError(error -> log.error("获取临时数据失败: key={}", key, error));
    }

    // ==================== 工具方法 ====================

    /**
     * 列出指定namespace的所有键
     *
     * @param namespaceAlias namespace别名
     * @return 键列表响应
     */
    public Mono<String> listKeysByNamespace(String namespaceAlias) {
        return kvClient.getByAlias(namespaceAlias, "")
                .switchIfEmpty(Mono.defer(() -> {
                    // 如果通过别名找不到，尝试直接使用作为namespace ID
                    return kvClient.listKeys(namespaceAlias, null, null);
                }))
                .doOnSuccess(response -> log.info("获取namespace键列表完成: namespace={}", namespaceAlias));
    }

    /**
     * 获取所有配置的namespace
     *
     * @return namespace映射
     */
    public Map<String, String> getAllNamespaces() {
        return kvClient.getAllNamespaces();
    }

    /**
     * 检查namespace是否存在
     *
     * @param namespaceAlias namespace别名
     * @return 是否存在
     */
    public boolean isNamespaceExists(String namespaceAlias) {
        return kvClient.getAllNamespaces().containsKey(namespaceAlias);
    }

    // ==================== 跨namespace操作示例 ====================

    /**
     * 跨namespace数据迁移示例
     *
     * @param sourceNamespace 源namespace
     * @param targetNamespace 目标namespace
     * @param key 键名
     * @return 操作结果
     */
    public Mono<Boolean> migrateDataBetweenNamespaces(String sourceNamespace, String targetNamespace, String key) {
        return kvClient.getByAlias(sourceNamespace, key)
                .flatMap(data -> kvClient.putByAlias(targetNamespace, key, data))
                .flatMap(success -> success ? 
                    kvClient.deleteByAlias(sourceNamespace, key) : 
                    Mono.just(false))
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("数据迁移成功: {} -> {}, key={}", sourceNamespace, targetNamespace, key);
                    } else {
                        log.error("数据迁移失败: {} -> {}, key={}", sourceNamespace, targetNamespace, key);
                    }
                });
    }

    /**
     * 数据备份到另一个namespace
     *
     * @param sourceNamespace 源namespace
     * @param backupNamespace 备份namespace
     * @param key 键名
     * @return 操作结果
     */
    public Mono<Boolean> backupDataToNamespace(String sourceNamespace, String backupNamespace, String key) {
        return kvClient.getByAlias(sourceNamespace, key)
                .flatMap(data -> {
                    String backupKey = "backup_" + System.currentTimeMillis() + "_" + key;
                    return kvClient.putByAlias(backupNamespace, backupKey, data);
                })
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("数据备份成功: {} -> {}, key={}", sourceNamespace, backupNamespace, key);
                    } else {
                        log.error("数据备份失败: {} -> {}, key={}", sourceNamespace, backupNamespace, key);
                    }
                });
    }
} 