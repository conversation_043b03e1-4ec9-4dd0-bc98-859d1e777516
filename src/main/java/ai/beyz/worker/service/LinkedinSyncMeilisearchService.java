package ai.beyz.worker.service;

import com.alibaba.fastjson2.JSON;
import com.meilisearch.sdk.Client;
import com.meilisearch.sdk.Index;

import ai.beyz.worker.entity.profile.LinkedinProfile;
import ai.beyz.worker.repository.profile.LinkedinProfileRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LinkedinSyncMeilisearchService {

    @Autowired
    private LinkedinProfileRepository linkedinProfileRepository;

    @Value("${app.meilisearch.index}")
    private String indexName;

    @Autowired
    private Client meilisearchClient;

    public void syncData(String profileId) {
        if(profileId == null) {
            log.warn("❌ Profile ID is null");
            return;
        }
        LinkedinProfile profile = linkedinProfileRepository.findById(profileId).orElse(null);
        if(profile == null) {
            log.warn("❌ Profile not found: {}", profileId);
            return;
        }
        // 同步数据到meilisearch
        // 1. 同步profile

        Index index = meilisearchClient.index(indexName);
        index.addDocuments(JSON.toJSONString(profile));
        log.info("✅ Successfully synced profile to meilisearch: {}", profile.getId());
    }

}
