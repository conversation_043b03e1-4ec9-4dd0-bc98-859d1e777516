package ai.beyz.worker.service;

import ai.beyz.worker.entity.beyz.MailingList;
import com.alibaba.fastjson2.JSON;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class BatchLinkedinScrapeService {

    @Autowired
    private SendDataToQueueService sendDataToQueueService;

    @Autowired
    private LinkedinScrapeServiceV2 linkedinScrapeServiceV2;

    @Autowired
    private RateLimiter rateLimiter;

    @Autowired
    @Qualifier("batchLinkedinScrapeExecutor")
    private Executor threadPoolExecutor;

    private static final int BATCH_SIZE = 1000;
    private static final int THREAD_POOL_SIZE = 3;

    // 信号量控制并发数量
    private final Semaphore semaphore = new Semaphore(THREAD_POOL_SIZE);

    /**
     * 批量处理LinkedIn数据爬取
     * 使用3个线程并发处理，每次获取1000条数据，使用限流器控制处理速度（每分钟30次）
     */
    public void processBatchLinkedinScrape() {
        log.info("🚀 Starting batch LinkedIn scraping process with {} threads...", THREAD_POOL_SIZE);

        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger totalSuccess = new AtomicInteger(0);
        AtomicInteger totalFailed = new AtomicInteger(0);
        AtomicInteger batchCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();
        
        try {
            while (true) {
                int currentBatch = batchCount.incrementAndGet();
                log.info("📦 Processing batch #{} (fetching {} records)...", currentBatch, BATCH_SIZE);
                
                // 获取未处理的数据
                List<Message<String>> messages = sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(BATCH_SIZE);
                
                if (messages.isEmpty()) {
                    log.info("✅ No more data to process. Batch processing completed!");
                    break;
                }
                
                log.info("📋 Found {} records in batch #{}", messages.size(), currentBatch);

                // 使用多线程并发处理消息
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (int i = 0; i < messages.size(); i++) {
                    final int index = i;
                    final Message<String> message = messages.get(i);

                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            // 获取信号量许可（控制并发数量）
                            semaphore.acquire();

                            try {
                                processMessage(message, index + 1, messages.size(), currentBatch,
                                             totalProcessed, totalSuccess, totalFailed, startTime);
                            } finally {
                                // 释放信号量许可
                                semaphore.release();
                            }

                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.error("❌ Thread interrupted while waiting for semaphore: {}", e.getMessage());
                            totalFailed.incrementAndGet();
                        } catch (Exception e) {
                            totalFailed.incrementAndGet();
                            log.error("❌ Error in async processing: {}", e.getMessage(), e);
                        }
                    }, threadPoolExecutor);

                    futures.add(future);
                }

                // 等待所有任务完成
                log.info("⏳ Waiting for all {} tasks in batch #{} to complete...", futures.size(), currentBatch);
                CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                try {
                    allTasks.get(); // 等待所有任务完成
                    log.info("✅ All tasks in batch #{} completed", currentBatch);
                } catch (Exception e) {
                    log.error("❌ Error waiting for batch #{} completion: {}", currentBatch, e.getMessage(), e);
                }
                
                // 批次完成统计
                long elapsed = System.currentTimeMillis() - startTime;
                log.info("📈 Batch #{} completed: {} items processed (Success: {}, Failed: {}, Total time: {}ms)", 
                        currentBatch, messages.size(), totalSuccess.get(), totalFailed.get(), elapsed);
                
                // 如果获取的数据少于批次大小，说明已经处理完所有数据
                if (messages.size() < BATCH_SIZE) {
                    log.info("✅ Reached end of data (batch size: {} < {}). Processing completed!", 
                            messages.size(), BATCH_SIZE);
                    break;
                }
            }
            
        } catch (Exception e) {
            log.error("❌ Fatal error in batch processing: {}", e.getMessage(), e);
            throw new RuntimeException("Batch processing failed", e);
        } finally {
            // 最终统计
            long totalTime = System.currentTimeMillis() - startTime;
            double avgTimePerItem = totalProcessed.get() > 0 ? (double) totalTime / totalProcessed.get() : 0;
            
            log.info("🏁 Batch LinkedIn scraping completed!");
            log.info("📊 Final Statistics:");
            log.info("   - Total Batches: {}", batchCount.get());
            log.info("   - Total Processed: {}", totalProcessed.get());
            log.info("   - Total Success: {}", totalSuccess.get());
            log.info("   - Total Failed: {}", totalFailed.get());
            log.info("   - Success Rate: {:.2f}%",
                    totalProcessed.get() > 0 ? (double) totalSuccess.get() / totalProcessed.get() * 100 : 0);
            log.info("   - Total Time: {:.2f} seconds", totalTime / 1000.0);
            log.info("   - Average Time per Item: {:.2f}ms", avgTimePerItem);
            log.info("   - Processing Rate: {:.2f} items/minute",
                    totalProcessed.get() > 0 ? (double) totalProcessed.get() / (totalTime / 60000.0) : 0);
        }
    }

    /**
     * 处理单个消息（多线程安全）
     */
    private void processMessage(Message<String> message, int itemIndex, int totalItems, int batchNumber,
                               AtomicInteger totalProcessed, AtomicInteger totalSuccess,
                               AtomicInteger totalFailed, long startTime) {
        try {
            // 解析消息内容
            Map<String, String> data = parseMessageData(message.getPayload());
            if (data == null) {
                log.warn("⚠️ Failed to parse message data: {}", message.getPayload());
                totalFailed.incrementAndGet();
                return;
            }

            String email = data.get("email");
            String url = data.get("url");

            if (email == null || url == null) {
                log.warn("⚠️ Missing email or url in message: {}", message.getPayload());
                totalFailed.incrementAndGet();
                return;
            }

            // 等待限流器许可（每分钟30次）
            log.debug("⏳ Thread {} waiting for rate limiter permit... (item {}/{})",
                     Thread.currentThread().getName(), itemIndex, totalItems);
            rateLimiter.acquire();

            // 调用workflow方法处理
            log.info("🔄 Thread {} processing item {}/{} - Email: {}, URL: {}",
                    Thread.currentThread().getName(), itemIndex, totalItems, email, url);

            String result = linkedinScrapeServiceV2.workflow(email, url);

            if (result != null) {
                totalSuccess.incrementAndGet();
                log.info("✅ Thread {} successfully processed: {} -> {}",
                        Thread.currentThread().getName(), email, result);
            } else {
                totalFailed.incrementAndGet();
                log.warn("❌ Thread {} failed to process: {}",
                        Thread.currentThread().getName(), email);
            }

            int processed = totalProcessed.incrementAndGet();

            // 每处理10条记录输出一次进度（线程安全）
            if (processed % 10 == 0) {
                long elapsed = System.currentTimeMillis() - startTime;
                double avgTimePerItem = (double) elapsed / processed;
                log.info("📊 Progress: {} items processed in batch #{} (avg: {:.2f}ms/item)",
                        processed, batchNumber, avgTimePerItem);
            }

        } catch (Exception e) {
            totalFailed.incrementAndGet();
            totalProcessed.incrementAndGet();
            log.error("❌ Thread {} error processing message: {}",
                     Thread.currentThread().getName(), e.getMessage(), e);
        }
    }

    /**
     * 解析消息数据
     */
    private Map<String, String> parseMessageData(String payload) {
        try {
            return JSON.parseObject(payload, HashMap.class);
        } catch (Exception e) {
            log.error("❌ Failed to parse message payload: {}", payload, e);
            return null;
        }
    }
    
    /**
     * 获取处理状态信息
     */
    public Map<String, Object> getProcessingStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("rateLimitConfig", "30 requests/minute (1 request/second)");
        status.put("batchSize", BATCH_SIZE);
        status.put("threadPoolSize", THREAD_POOL_SIZE);
        status.put("concurrentThreads", "3 threads processing concurrently");
        status.put("semaphoreAvailablePermits", semaphore.availablePermits());
        status.put("isRunning", false); // 这里可以根据实际情况添加运行状态跟踪
        return status;
    }
}
