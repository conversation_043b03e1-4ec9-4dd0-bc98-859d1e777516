package ai.beyz.worker.service;

import ai.beyz.worker.config.AppProperties;
import ai.beyz.worker.entity.profile.LinkedinCompany;
import ai.beyz.worker.repository.profile.LinkedinCompanyRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.Optional;

@Service
@Slf4j
public class CompanyEnrichmentService {

    @Autowired
    private LinkedinCompanyRepository linkedinCompanyRepository;

    @Autowired
    private WebClient webClient;

    @Autowired
    private AppProperties props;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 获取或插入公司信息 - 对应Python的fetch_or_insert_company()
     * 
     * @param companyId LinkedIn公司ID
     * @return 公司信息，如果获取失败返回null
     */
    public LinkedinCompany fetchOrInsertCompany(String companyId) {
        if (companyId == null || companyId.trim().isEmpty()) {
            log.warn("Company ID is null or empty");
            return null;
        }

        try {
            log.debug("🔍 Looking up company with ID: {}", companyId);

            // 首先查找数据库中是否已存在该公司
            Optional<LinkedinCompany> existingCompany = linkedinCompanyRepository.findByCompanyId(companyId);
            if (existingCompany.isPresent()) {
                LinkedinCompany company = existingCompany.get();
                log.debug("✅ Found existing company in database:");
                log.debug("   - Company ID: {}", company.getCompanyId());
                log.debug("   - Name: '{}'", company.getName());
                log.debug("   - Universal Name: '{}'", company.getUniversalName());
                log.debug("   - Company Size: '{}'", company.getCompanySize());
                log.debug("   - Industry: '{}'", company.getIndustry());
                return company;
            }

            // 如果数据库中不存在，从API获取
            log.debug("🌐 Company not found in database, fetching from LinkedIn API...");
            LinkedinCompany apiCompany = fetchCompanyFromApi(companyId);
            
            if (apiCompany != null) {
                // 保存到数据库
                LinkedinCompany savedCompany = linkedinCompanyRepository.save(apiCompany);
                log.debug("✅ Successfully fetched and saved company: {} (ID: {})", 
                        savedCompany.getName(), savedCompany.getCompanyId());
                return savedCompany;
            } else {
                log.warn("❌ Failed to fetch company from API for ID: {}", companyId);
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error in fetchOrInsertCompany for ID {}: {}", companyId, e.getMessage());
            return null;
        }
    }

    /**
     * 从LinkedIn API获取公司信息 - 带重试机制
     */
    @Retryable(value = { RuntimeException.class }, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 2000, multiplier = 2))
    private LinkedinCompany fetchCompanyFromApi(String companyId) {
        try {
            log.debug("🌐 Calling LinkedIn Company API for ID: {}", companyId);

            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .scheme("https")
                            .host(props.getApi().getLinkedinScraper().getHost())
                            .path("/api/v1/company/profile")
                            .queryParam("company_id", companyId)
                            .build())
                    .header("x-rapidapi-key", props.getApi().getLinkedinScraper().getKey())
                    .header("x-rapidapi-host", props.getApi().getLinkedinScraper().getHost())
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (response != null) {
                return parseCompanyResponse(response, companyId);
            } else {
                log.warn("❌ Empty response from LinkedIn Company API for ID: {}", companyId);
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error calling LinkedIn Company API for ID {}: {}", companyId, e.getMessage());
            throw new RuntimeException("Failed to call LinkedIn Company API", e);
        }
    }

    /**
     * 解析LinkedIn公司API响应
     */
    private LinkedinCompany parseCompanyResponse(String response, String companyId) {
        try {
            JsonNode rootNode = objectMapper.readTree(response);
            
            // 检查API响应状态
            boolean success = rootNode.path("success").asBoolean(false);
            if (!success) {
                log.warn("❌ LinkedIn Company API returned success=false for ID: {}", companyId);
                return null;
            }

            JsonNode dataNode = rootNode.path("data");
            if (dataNode.isMissingNode() || dataNode.isNull()) {
                log.warn("❌ No data field in LinkedIn Company API response for ID: {}", companyId);
                return null;
            }

            // 创建LinkedinCompany实体 - 只映射数据库中存在的字段
            LinkedinCompany company = new LinkedinCompany();
            
            // 基本信息
            company.setCompanyId(getStringValue(dataNode, "id"));
            company.setName(getStringValue(dataNode, "name"));
            company.setUniversalName(getStringValue(dataNode, "universal_name"));
            
            // 员工数量范围 -> company_size字段
            JsonNode employeeCountRange = dataNode.path("employee_count_range");
            if (!employeeCountRange.isMissingNode()) {
                Integer start = getIntegerValue(employeeCountRange, "start");
                Integer end = getIntegerValue(employeeCountRange, "end");
                if (start != null && end != null) {
                    company.setCompanySize(start + "-" + end + " employees");
                }
            } else {
                // 备用：使用employee_count
                Integer employeeCount = getIntegerValue(dataNode, "employee_count");
                if (employeeCount != null) {
                    company.setCompanySize(employeeCount + " employees");
                }
            }
            
            // 行业信息 - 取第一个行业
            JsonNode industriesNode = dataNode.path("industries");
            if (industriesNode.isArray() && industriesNode.size() > 0) {
                String firstIndustry = industriesNode.get(0).asText();
                company.setIndustry(firstIndustry);
            }

            log.debug("✅ Successfully parsed company data: {} (Size: {}, Industry: {})", 
                    company.getName(), company.getCompanySize(), company.getIndustry());
            return company;

        } catch (Exception e) {
            log.error("❌ Error parsing LinkedIn Company API response for ID {}: {}", companyId, e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 安全获取字符串值的辅助方法
     */
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.path(fieldName);
        if (fieldNode.isMissingNode() || fieldNode.isNull()) {
            return null;
        }
        String value = fieldNode.asText();
        return value.isEmpty() ? null : value;
    }



    /**
     * 验证公司是否存在于数据库中
     */
    public boolean verifyCompanyExists(String companyId) {
        if (companyId == null || companyId.trim().isEmpty()) {
            return false;
        }
        
        try {
            Optional<LinkedinCompany> company = linkedinCompanyRepository.findByCompanyId(companyId);
            boolean exists = company.isPresent();
            log.debug("🔍 Company verification for ID {}: {}", companyId, exists ? "EXISTS" : "NOT FOUND");
            return exists;
        } catch (Exception e) {
            log.error("❌ Error verifying company existence for ID {}: {}", companyId, e.getMessage());
            return false;
        }
    }

    /**
     * 安全获取整型值的辅助方法
     */
    private Integer getIntegerValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.path(fieldName);
        if (fieldNode.isMissingNode() || fieldNode.isNull()) {
            return null;
        }
        try {
            return fieldNode.asInt();
        } catch (Exception e) {
            return null;
        }
    }

} 