package ai.beyz.worker.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import ai.beyz.worker.dto.EmailValidationResponse;
import ai.beyz.worker.config.AppProperties;
import java.time.Duration;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.http.HttpStatusCode;
import reactor.core.publisher.Mono;
import ai.beyz.worker.dto.LinkedInScrapeResponse;
import ai.beyz.worker.entity.profile.LinkedinProfile;
import ai.beyz.worker.entity.beyz.MailingList;
import ai.beyz.worker.repository.beyz.MailingListRepository;
import ai.beyz.worker.repository.profile.LinkedinProfileRepository;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import ai.beyz.worker.config.CloudflareKvClient;

@Service
@Slf4j
public class LinkedinScrapeService {

    @Autowired
    private WebClient webClient;

    @Autowired
    private AppProperties props;

    @Autowired
    private LinkedinProfileRepository linkedinProfileRepository;

    @Autowired
    private ProfileEnrichmentService profileEnrichmentService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MailingListRepository mailingListRepository;

    @Autowired
    private CloudflareKvClient cloudflareKvClient;

    @Autowired
    private OpenAiEmbeddingModel embeddingModel;

    /**
     * step1: 验证邮箱地址有效性
     */
    public boolean validateEmail(String email) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("Validating email: {}", email);

            // 从Cloudflare KV中获取缓存数据,如果存在则直接返回
            /*String cachedData = cloudflareKvClient.getByAlias("email-verifier", email).block();
            if (cachedData != null) {
                EmailValidationResponse response = JSON.parseObject(cachedData, EmailValidationResponse.class);
                if (response != null && "ok".equals(response.getResult())) {
                    log.debug("✅ Email validation for {} from cache", email);
                    return true;
                } else {
                    log.warn("❌ Email validation for {} from cache", email);
                    return false;
                }
            }*/
            // 如果缓存不存在,则调用API获取结果并存储到Cloudflare KV
            EmailValidationResponse response = webClient.get()
                    .uri(props.getApi().getEmailVerifier().getUrl(), email)
                    .retrieve()
                    .bodyToMono(EmailValidationResponse.class)
                    .timeout(Duration.ofSeconds(10)) // 设置10秒超时
                    .block(); // 同步等待结果
            // 将结果存储到Cloudflare KV
            //cloudflareKvClient.putByAlias("email-verifier", email, response.getResult()).block();

            long duration = System.currentTimeMillis() - startTime;
            log.debug("⏱️ Email validation for {} took {}ms", email, duration);
            return response != null && "ok".equals(response.getResult());
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Error validating email {} after {}ms: {}", email, duration, e.getMessage());
            return false;
        }
    }

    /**
     * step2: 根据LinkedIn URL获取profile数据
     * 
     * @param email
     * @return
     */
    @Retryable(value = { WebClientRequestException.class }, // 仅在网络连接相关异常时重试
            maxAttempts = 3, backoff = @Backoff(delay = 2000, multiplier = 2) // 首次延迟2秒，后续延迟时间加倍
    )
    public LinkedInScrapeResponse.ProfileData scrapeLinkedinProfile(String url) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("Scraping linkedin profile for {}", url);

            // 从Cloudflare KV中获取缓存数据,如果存在则直接返回
            /*String cachedData = cloudflareKvClient.getByAlias("linkedin-verifier", url).block();
            if (cachedData != null) {
                LinkedInScrapeResponse response = JSON.parseObject(cachedData, LinkedInScrapeResponse.class);
                if (response != null && response.getSuccess() != null && response.getSuccess()) {
                    log.debug("✅ Successfully scraped LinkedIn profile for {} from cache", url);
                    return response.getData();
                } else {
                    log.warn("❌ LinkedIn API returned unsuccessful response for {} from cache", url);
                    return null;
                }
            }
*/
            // 如果缓存不存在,则调用API获取结果并存储到Cloudflare KV
            String username = extractUsernameFromUrl(url);
            if (username == null) {
                log.warn("Could not extract username from URL: {}", url);
                return null;
            }
            log.debug("Extracted username: {}", username);

            // 调用LinkedIn API获取profile数据
            LinkedInScrapeResponse response = webClient.get()
                    .uri(props.getApi().getLinkedinScraper().getUrl(), uriBuilder -> uriBuilder
                            .queryParam("username", username)
                            .queryParam("include_experiences", "true")
                            .queryParam("include_skills", "true")
                            .queryParam("include_certifications", "true")
                            .queryParam("include_educations", "true")
                            .queryParam("include_publications", "true")
                            .queryParam("include_honors", "true")
                            .queryParam("include_volunteers", "true")
                            .queryParam("_admin",true)
                            .build())
                    .header("x-rapidapi-key", props.getApi().getLinkedinScraper().getKey())
                    .header("x-rapidapi-host", props.getApi().getLinkedinScraper().getHost())
                    .retrieve()
                    // 如果HTTP状态码是4xx或5xx, retrieve()会抛出WebClientResponseException
                    // 我们捕获它，记录日志并返回一个空的Mono，这样就不会触发重试
                    .onStatus(HttpStatusCode::isError, clientResponse -> {
                        log.error("❌ Failed to scrape LinkedIn for {}: HTTP {}",
                                username, clientResponse.statusCode().value());
                        return Mono.empty(); // 返回空，表示处理完成，不要抛异常
                    })
                    .bodyToMono(LinkedInScrapeResponse.class)
                    .timeout(Duration.ofSeconds(15)) // 设置15秒超时
                    .block(); // 同步等待
            
            if (response == null) {
                log.warn("❌ No response from LinkedIn for {}", username);
                return null;
            }

            // 将结果存储到Cloudflare KV
            //cloudflareKvClient.putByAlias("linkedin-verifier", url, JSON.toJSONString(response.getData())).block();

            if (response.getSuccess() != null && response.getSuccess()) {
                log.debug("✅ Successfully scraped LinkedIn profile for {}", username);
                return response.getData();
            } else {
                log.warn("❌ LinkedIn API returned unsuccessful response for {}", username);
                return null;
            }

        } catch (Exception e) {
            log.error("❌ Error scraping linkedin profile for {}: {}", url, e.getMessage());
            e.printStackTrace();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("⏱️ Scraping linkedin profile for {} took {}ms", url, duration);
        }
        return null;
    }

    /**
     * step3: 完整的工作流程 - 验证邮箱、爬取数据、转换并保存
     */
    public LinkedinProfile workflow(String email, String url) {
        log.debug("🚀 Starting LinkedIn scraping workflow for email: {}, url: {}", email, url);

        // 1. 验证邮箱地址有效性
        if (!validateEmail(email)) {

            log.warn("❌ Invalid email address: {}", email);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("Invalid email address");
                mailingListRepository.save(mailingList);
            }
            return null;
        }

        // 2. 根据LinkedIn URL获取profile数据
        LinkedInScrapeResponse.ProfileData profileData = scrapeLinkedinProfile(url);
        if (profileData == null) {
            log.warn("❌ Failed to scrape LinkedIn profile for {}", url);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("Failed to scrape LinkedIn profile");
                mailingListRepository.save(mailingList);
            }
            return null;
        }

        // 3. 解析profile数据
        LinkedinProfile linkedinProfile = convertToLinkedinProfile(profileData, email, url);
        if (linkedinProfile == null) {
            log.error("❌ Failed to convert profile data for {}", url);
            MailingList mailingList = mailingListRepository.findByEmailAndLinkedinUrl(email, url);
            if (mailingList != null) {
                mailingList.setStatus(1);
                mailingList.setComments("Failed to convert profile data");
                mailingListRepository.save(mailingList);
            }
            return null;
        }

        // 4. 保存profile数据（智能处理外键约束）
        LinkedinProfile savedProfile = saveProfileWithCompanyHandling(linkedinProfile, email);

        //5. openAi embedding summary
        //savedProfile = embeddingSummary(savedProfile);

        // 5. 更新MailingList表
        if (savedProfile != null) {
            MailingList mailingList = mailingListRepository.findByEmail(email);
            if (mailingList != null) {
                mailingList.setScraped(true);
                mailingListRepository.save(mailingList);
            }
            log.info("🔍 Saved profile: {}", savedProfile.getId());
            return savedProfile;
        } else {
            return null;
        }

    }

    /**
    private LinkedinProfile embeddingSummary(LinkedinProfile savedProfile) {
        try {
            // 检查 summary 是否为空
            if (savedProfile.getSummary() == null || savedProfile.getSummary().trim().isEmpty()) {
                log.warn("⚠️ Profile summary is empty for profile: {}", savedProfile.getFullName());
                return savedProfile;
            }

            EmbeddingResponse embeddingResponse = this.embeddingModel.embedForResponse(List.of(savedProfile.getSummary()));
            
            // 检查响应是否为空
            if (embeddingResponse == null || embeddingResponse.getResults().isEmpty()) {
                log.warn("⚠️ No embedding response for profile: {}", savedProfile.getFullName());
                return savedProfile;
            }

            // 获取第一个结果的向量数据（已经是 float[] 类型）
            float[] floatArray = embeddingResponse.getResults().get(0).getOutput();
            savedProfile.setEmbeddingSmall(floatArray);
            linkedinProfileRepository.save(savedProfile);
            
            log.debug("✅ Successfully generated embedding for profile: {}", savedProfile.getFullName());
            
        } catch (Exception e) {
            log.error("❌ Failed to generate embedding for profile {}: {}", savedProfile.getFullName(), e.getMessage());
        }
        return savedProfile;
    }
**/
    /**
     * 智能保存Profile，自动处理公司外键约束，并确保数据丰富化
     */
    private LinkedinProfile saveProfileWithCompanyHandling(LinkedinProfile linkedinProfile, String email) {
        LinkedinProfile savedProfile = null;

        try {
            // 首先尝试直接保存
            savedProfile = linkedinProfileRepository.save(linkedinProfile);
            log.debug("✅ Successfully saved LinkedIn profile for {} with ID: {}", email, savedProfile.getId());

        } catch (Exception e) {
            // 检查是否是外键约束错误
            if (e.getMessage() != null && e.getMessage().contains("foreign key constraint")
                    && e.getMessage().contains("fk_current_company")) {

                log.warn("⚠️ Company foreign key constraint violation, trying to resolve for {}", email);
                savedProfile = handleCompanyConstraintViolation(linkedinProfile, email);

            } else {
                log.error("❌ Failed to save LinkedIn profile for {}: {}", email, e.getMessage());
                return null;
            }
        }

        // 如果成功保存了Profile，则进行数据丰富化
        if (savedProfile != null) {
            savedProfile = enrichProfileData(savedProfile);
        }
        log.debug("✅ Successfully saved LinkedIn profile for {} with ID: {}", email, savedProfile.getId());

        return savedProfile;
    }

    /**
     * 处理公司外键约束违反的情况
     * 按照简化逻辑：没有公司ID的情况下，清除current_company字段
     */
    private LinkedinProfile handleCompanyConstraintViolation(LinkedinProfile linkedinProfile, String email) {
        String companyName = linkedinProfile.getCurrentCompany();
        log.warn("⚠️ Company constraint violation for '{}', clearing current_company field for {}", companyName, email);
        return saveFallbackProfile(linkedinProfile, email);
    }

    /**
     * 备用保存方案：清除current_company并保存
     */
    private LinkedinProfile saveFallbackProfile(LinkedinProfile linkedinProfile, String email) {
        try {
            // 备份原始公司名称
            String originalCompany = linkedinProfile.getCurrentCompany();

            // 清除current_company并保存
            linkedinProfile.setCurrentCompany(null);
            LinkedinProfile savedProfile = linkedinProfileRepository.save(linkedinProfile);

            log.debug("✅ Successfully saved LinkedIn profile (without current_company) for {} with ID: {}",
                    email, savedProfile.getId());
            log.warn("⚠️ Original company '{}' was not saved due to constraint issues", originalCompany);

            return savedProfile;

        } catch (Exception finalException) {
            log.error("❌ Failed to save LinkedIn profile even without current_company for {}: {}",
                    email, finalException.getMessage());
            return null;
        }
    }

    /**
     * 统一的数据丰富化处理 - 带外键约束保护
     */
    private LinkedinProfile enrichProfileData(LinkedinProfile savedProfile) {
        try {
            log.debug("🔍 Starting profile enrichment for: {}", savedProfile.getFullName());
            profileEnrichmentService.enrichProfile(savedProfile);

            // 保存丰富化后的数据 - 带外键约束处理
            return saveEnrichedProfileWithConstraintHandling(savedProfile);

        } catch (Exception enrichmentException) {
            log.warn("⚠️ Failed to enrich profile {}: {}", savedProfile.getFullName(),
                    enrichmentException.getMessage());
            enrichmentException.printStackTrace();

            // 即使丰富化失败，也返回已保存的基础profile
            return savedProfile;
        }
    }

    /**
     * 保存丰富化后的Profile，处理外键约束问题
     */
    private LinkedinProfile saveEnrichedProfileWithConstraintHandling(LinkedinProfile profile) {
        try {
            // 尝试保存丰富化后的Profile
            LinkedinProfile enrichedProfile = linkedinProfileRepository.save(profile);
            log.debug("✅ Successfully enriched and saved profile: {}", enrichedProfile.getFullName());
            return enrichedProfile;

        } catch (Exception e) {
            // 检查是否是外键约束错误
            if (e.getMessage() != null && e.getMessage().contains("foreign key constraint")
                    && e.getMessage().contains("fk_current_company")) {

                log.warn(
                        "⚠️ Company foreign key constraint violation during enrichment, clearing current_company for {}",
                        profile.getFullName());

                // 清除current_company字段并重新保存
                String originalCompany = profile.getCurrentCompany();
                profile.setCurrentCompany(null);

                try {
                    LinkedinProfile savedProfile = linkedinProfileRepository.save(profile);
                    log.debug("✅ Successfully saved enriched profile (without current_company) for {}",
                            savedProfile.getFullName());
                    log.warn("⚠️ Company '{}' was cleared due to foreign key constraint", originalCompany);
                    return savedProfile;

                } catch (Exception finalException) {
                    log.error("❌ Failed to save enriched profile even without current_company: {}",
                            finalException.getMessage());
                    throw finalException;
                }

            } else {
                log.error("❌ Unexpected error saving enriched profile: {}", e.getMessage());
                throw e;
            }
        }
    }

    // --- Helper Methods ---

    private String extractUsernameFromUrl(String url) {
        try {
            String path = url.replaceFirst("^(http[s]?://)?(www\\.)?linkedin\\.com/in/", "");
            return path.split("/")[0];
        } catch (Exception e) {
            log.warn("Could not extract username from URL: {}", url);
            return null;
        }
    }

    /**
     * 将API返回的ProfileData转换为LinkedinProfile实体
     */
    private LinkedinProfile convertToLinkedinProfile(LinkedInScrapeResponse.ProfileData profileData, String email,
            String linkedinUrl) {
        try {
            LinkedinProfile profile = new LinkedinProfile();

            // 基本信息字段 - 过滤 \u0000 字符
            profile.setId(profileData.getId() != null ? profileData.getId() : UUID.randomUUID().toString());
            profile.setUrn(profileData.getUrn() != null ? profileData.getUrn().replace("\u0000", "") : null);
            profile.setPublicIdentifier(profileData.getPublicIdentifier() != null ? profileData.getPublicIdentifier().replace("\u0000", "") : null);
            profile.setFirstName(profileData.getFirstName() != null ? profileData.getFirstName().replace("\u0000", "") : null);
            profile.setLastName(profileData.getLastName() != null ? profileData.getLastName().replace("\u0000", "") : null);
            profile.setFullName(profileData.getFullName() != null ? profileData.getFullName().replace("\u0000", "") : null);
            profile.setHeadline(profileData.getHeadline() != null ? profileData.getHeadline().replace("\u0000", "") : null);

            // 状态字段
            profile.setIsPremium(profileData.getIsPremium());
            profile.setIsOpenToWork(profileData.getIsOpenToWork());
            profile.setIsHiring(profileData.getIsHiring());
            profile.setIsMemorialized(profileData.getIsMemorialized());
            profile.setIsInfluencer(profileData.getIsInfluencer());
            profile.setIsTopVoice(profileData.getIsTopVoice());
            profile.setIsCreator(profileData.getIsCreator());

            // 出生信息
            if (profileData.getBirth() != null) {
                profile.setBirthDay(profileData.getBirth().getDay());
                profile.setBirthMonth(profileData.getBirth().getMonth());
                profile.setBirthYear(profileData.getBirth().getYear());
            }

            // 其他基本信息 - 过滤 \u0000 字符
            profile.setPronoun(profileData.getPronoun() != null ? profileData.getPronoun().replace("\u0000", "") : null);
            profile.setCreated(profileData.getCreated()); // Long 类型，不需要过滤

            // 解析创建日期
            if (profileData.getCreatedDate() != null) {
                try {
                    profile.setCreatedDate(LocalDateTime.parse(profileData.getCreatedDate(),
                            DateTimeFormatter.ISO_DATE_TIME));
                } catch (Exception e) {
                    log.warn("Failed to parse created_date: {}", profileData.getCreatedDate());
                }
            }

            // 地址信息 - 过滤 \u0000 字符
            if (profileData.getLocation() != null) {
                profile.setLocationCountry(profileData.getLocation().getCountry() != null ?
                    profileData.getLocation().getCountry().replace("\u0000", "") : null);
                profile.setLocationCountryCode(profileData.getLocation().getCountryCode() != null ?
                    profileData.getLocation().getCountryCode().replace("\u0000", "") : null);
                profile.setLocationCity(profileData.getLocation().getCity() != null ?
                    profileData.getLocation().getCity().replace("\u0000", "") : null);
            }

            // 序列化复杂对象为JSON字符串
            profile.setAvatar(serializeToJson(profileData.getAvatar()));
            profile.setCover(serializeToJson(profileData.getCover()));

            // 处理数组字段 - associatedHashtag，过滤 \u0000 字符
            if (profileData.getAssociatedHashtag() != null) {
                String[] filteredHashtags = profileData.getAssociatedHashtag().stream()
                        .map(hashtag -> hashtag != null ? hashtag.replace("\u0000", "") : hashtag)
                        .toArray(String[]::new);
                profile.setAssociatedHashtag(filteredHashtags);
            }

            profile.setSupportedLocales(serializeToJson(profileData.getSupportedLocales()));

            // 网站信息 - 过滤 \u0000 字符
            if (profileData.getWebsite() != null) {
                profile.setWebsiteTitle(profileData.getWebsite().getTitle() != null ?
                    profileData.getWebsite().getTitle().replace("\u0000", "") : null);
                profile.setWebsiteUrl(profileData.getWebsite().getUrl() != null ?
                    profileData.getWebsite().getUrl().replace("\u0000", "") : null);
            }

            // 主要语言环境 - 过滤 \u0000 字符
            if (profileData.getPrimaryLocale() != null) {
                profile.setPrimaryLocaleCountry(profileData.getPrimaryLocale().getCountry() != null ?
                    profileData.getPrimaryLocale().getCountry().replace("\u0000", "") : null);
                profile.setPrimaryLocaleLanguage(profileData.getPrimaryLocale().getLanguage() != null ?
                    profileData.getPrimaryLocale().getLanguage().replace("\u0000", "") : null);
            }

            // 关注者和连接数
            if (profileData.getFollowerAndConnection() != null) {
                profile.setFollowerCount(profileData.getFollowerAndConnection().getFollowerCount());
                profile.setConnectionCount(profileData.getFollowerAndConnection().getConnectionCount());
            }

            // 详细信息（序列化为JSON）
            profile.setExperiences(serializeToJson(profileData.getExperiences()));
            profile.setSkills(serializeToJson(profileData.getSkills()));
            profile.setCertifications(serializeToJson(profileData.getCertifications()));
            profile.setPublications(serializeToJson(profileData.getPublications()));
            profile.setEducations(serializeToJson(profileData.getEducations()));
            profile.setHonors(serializeToJson(profileData.getHonors()));
            profile.setVolunteers(serializeToJson(profileData.getVolunteers()));

            // 外部提供的信息
            profile.setEmail(email);
            profile.setLinkedinUrl(linkedinUrl);

            // 设置时间戳
            profile.setUpdatedDate(LocalDateTime.now());
            profile.setEnriched(true);

            // 提取和计算一些有用的字段
            extractDerivedFields(profile, profileData);

            log.debug("✅ Successfully converted ProfileData to LinkedinProfile for {}", email);
            return profile;

        } catch (Exception e) {
            log.error("❌ Error converting ProfileData to LinkedinProfile for {}: {}", email, e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将对象序列化为JSON字符串 - 过滤 \u0000 字符
     */
    private String serializeToJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            String jsonString = objectMapper.writeValueAsString(obj);
            // 过滤掉 \u0000 字符
            return jsonString != null ? jsonString.replace("\u0000", "") : null;
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize object to JSON: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取和计算衍生字段
     */
    private void extractDerivedFields(LinkedinProfile profile, LinkedInScrapeResponse.ProfileData profileData) {
        try {
            // 提取当前职位（不设置公司，让ProfileEnrichmentService处理）
            if (profileData.getExperiences() != null && !profileData.getExperiences().isEmpty()) {
                LinkedInScrapeResponse.Experience currentExp = profileData.getExperiences().get(0);
                if (currentExp != null) {
                    profile.setCurrentTitle(currentExp.getTitle());
                    // 不在这里设置current_company，由ProfileEnrichmentService按简化逻辑处理
                }
            }

            // 提取最高学历
            if (profileData.getEducations() != null && !profileData.getEducations().isEmpty()) {
                LinkedInScrapeResponse.Education education = profileData.getEducations().get(0);
                if (education != null) {
                    profile.setHighestDegree(education.getDegree());
                    profile.setFieldOfStudy(education.getDescription()); // 可能需要调整
                }
            }

            // 生成公司列表和学校列表
            profile.setCompanyList(serializeToJson(
                    profileData.getExperiences() != null ? profileData.getExperiences().stream()
                            .filter(exp -> exp.getCompany() != null)
                            .map(exp -> exp.getCompany().getName())
                            .distinct()
                            .toList() : null));

            profile.setSchoolList(serializeToJson(
                    profileData.getEducations() != null ? profileData.getEducations().stream()
                            .map(LinkedInScrapeResponse.Education::getSchool)
                            .filter(school -> school != null)
                            .distinct()
                            .toList() : null));

        } catch (Exception e) {
            log.warn("Failed to extract derived fields: {}", e.getMessage());
        }
    }
}
