package ai.beyz.worker.service;

import ai.beyz.worker.entity.beyz.MailingList;
import ai.beyz.worker.repository.beyz.MailingListRepository;
import com.alibaba.fastjson2.JSON;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SendDataToQueueService {

    @Autowired
    private SqsTemplate sqsTemplate;

    @Autowired
    private MailingListRepository mailingListRepository;


    public void sendDataToQueue(int limit) {
        List<Message<String>> messageList = findUnscrapedWithEmailAndLinkedinUrl(limit);
        if(messageList.size() > 0) {
            //sqs 一次最多发送 10条数据
            int size = messageList.size();
            for(int i = 0; i < size; i += 10) {
                List<Message<String>> subList = messageList.subList(i, Math.min(i + 10, size));
                sqsTemplate.sendMany("linkedin_url_queue", subList);
            }
        }
        log.info("✅ Sent {} messages to queue", messageList.size());
    }


    public List<Message<String>> findUnscrapedWithEmailAndLinkedinUrl(int limit) {
        List<MailingList> mailingLists = mailingListRepository.findUnscrapedWithEmailAndLinkedinUrl(limit);
        List<Message<String>> messageList = new ArrayList<>();

        for(MailingList mailingList : mailingLists) {
            if(mailingList.getEmail() == null || mailingList.getEmail().isEmpty() || mailingList.getLinkedinUrl() == null || mailingList.getLinkedinUrl().isEmpty()) {
                continue;
            }
            if(!mailingList.getLinkedinUrl().startsWith("https://www.linkedin.com/in/")) {
                continue;
            }
            Map<String, String> map = new HashMap<>();
            map.put("email", mailingList.getEmail());
            map.put("url", mailingList.getLinkedinUrl());

            messageList.add(MessageBuilder.withPayload(JSON.toJSONString(map)).build());

        }
        return messageList;
    }
}
