package ai.beyz.worker.service;

import ai.beyz.worker.dto.LinkedInScrapeResponse;
import ai.beyz.worker.entity.profile.LinkedinProfile;
import ai.beyz.worker.entity.profile.LinkedinCompany;
import ai.beyz.worker.config.AppProperties;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.genai.Client;

import com.google.genai.types.GenerateContentResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProfileEnrichmentService {

    @Autowired
    private CompanyEnrichmentService companyEnrichmentService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AppProperties props;
    /**
     * 丰富化Profile数据 - 计算衍生字段
     */
    public void enrichProfile(LinkedinProfile profile) {
        try {
            log.debug("🔍 Enriching profile: {}", profile.getFullName());

            // 解析JSON数据
            List<LinkedInScrapeResponse.Experience> experiences = parseExperiences(profile.getExperiences());
            List<LinkedInScrapeResponse.Education> educations = parseEducations(profile.getEducations());

            // 计算衍生字段
            enrichWorkExperience(profile, experiences);
            enrichEducationData(profile, educations);
            enrichPersonalData(profile, educations);
            enrichCompanyAndSchoolLists(profile, experiences, educations);

            // 设置enriched标志
            profile.setEnriched(true);
            profile.setUpdatedDate(LocalDateTime.now());

            // 使用 AI 生成 summery
            String summary = generateSummary(profile);
            profile.setSummary(summary);

            log.debug("✅ Successfully enriched profile: {}", profile.getFullName());

        } catch (Exception e) {
            log.error("❌ Error enriching profile {}: {}", profile.getFullName(), e.getMessage());
            e.printStackTrace();
        }
    }

    private String generateSummary(LinkedinProfile profile) {
        String prompt = "You are a helpful assistant that generates a summary of a LinkedIn profile.\n" +
                "The profile is:\n" +
                JSON.toJSONString(profile) +
                "\n" +
                "Output the summary in text format. Do not output any other text.\n" +
                "The summary should be a very detailed summary of the profile. The summary does not contain personal contact information.";

        log.debug("🔍 Generating summary for profile prompt: {}", prompt);
        Client client = Client.builder().apiKey(props.getApi().getGoogle().getKey()).build();
        GenerateContentResponse generateContentResponse = client.models.generateContent(props.getApi().getGoogle().getModel(), prompt, null);
        return generateContentResponse.text();
    }

    /**
     * 丰富工作经历相关数据
     */
    private void enrichWorkExperience(LinkedinProfile profile, List<LinkedInScrapeResponse.Experience> experiences) {
        if (experiences == null || experiences.isEmpty()) {
            log.debug("💼 No experiences found");
            return;
        }

        log.debug("💼 Processing {} experiences", experiences.size());

        // 查找当前工作
        LinkedInScrapeResponse.Experience currentJob = getCurrentJob(experiences);
        if (currentJob != null) {
            log.debug("💼 Found current job: {} at {}", currentJob.getTitle(), 
                currentJob.getCompany() != null ? currentJob.getCompany().getName() : "Unknown Company");
            profile.setCurrentTitle(currentJob.getTitle());
            
            if (currentJob.getCompany() != null) {
                // 优先使用公司ID获取详细信息
                String companyId = currentJob.getCompany().getId();
                String companyName = currentJob.getCompany().getName();
                
                log.debug("🏢 Current job company - ID: '{}', Name: '{}'", companyId, companyName);
                
                if (companyId != null && !companyId.trim().isEmpty()) {
                    // 有公司ID的情况：查数据库 -> 没有就调用API创建
                    log.debug("🏢 Found company ID: {}, name: {}, processing...", companyId, companyName);
                    LinkedinCompany company = companyEnrichmentService.fetchOrInsertCompany(companyId);
                    
                    if (company != null && company.getName() != null && company.getCompanyId() != null) {
                        // 使用公司ID而不是名称，避免外键约束问题
                        log.debug("✅ Successfully got company from service: {} (ID: {})", company.getName(), company.getCompanyId());
                        profile.setCurrentCompany(company.getCompanyId());  // 使用ID而不是名称
                        profile.setIndustry(company.getIndustry()); // 设置行业
                        log.debug("✅ Set current company ID: {}", company.getCompanyId());
                    } else {
                        log.warn("❌ Failed to get valid company for ID: {}, name: {}, clearing current_company", companyId, companyName);
                        log.warn("❌ Company object: {}", company);
                        profile.setCurrentCompany(null);
                    }
                } else {
                    // 没有公司ID的情况：清除current_company字段
                    log.debug("ℹ️ No company ID available for company: {}, clearing current_company", companyName);
                    profile.setCurrentCompany(null);
                }
                
                // 计算当前工作时长
                LocalDateTime startDate = parseStartDate(currentJob.getDate());
                if (startDate != null) {
                    double durationYears = ChronoUnit.DAYS.between(startDate, LocalDateTime.now()) / 365.0;
                    profile.setCurrentJobDurationYears(Math.round(durationYears * 100.0) / 100.0);
                    log.debug("💼 Current job duration: {} years", profile.getCurrentJobDurationYears());
                }
            } else {
                log.warn("💼 Current job has no company information");
            }
        } else {
            log.debug("💼 No current job found");
        }

        // 计算总工作年限
        double totalYearsOfExperience = calculateTotalYearsOfExperience(experiences);
        profile.setYoe(totalYearsOfExperience);
        log.debug("💼 Total years of experience: {}", totalYearsOfExperience);
    }

    /**
     * 丰富教育相关数据
     */
    private void enrichEducationData(LinkedinProfile profile, List<LinkedInScrapeResponse.Education> educations) {
        if (educations == null || educations.isEmpty()) {
            return;
        }

        // 获取最高学历
        String highestDegree = getHighestDegree(educations);
        profile.setHighestDegree(highestDegree);

        // 获取专业领域
        String fieldOfStudy = getFieldOfStudy(educations);
        profile.setFieldOfStudy(fieldOfStudy);

        // 判断是否为学生
        boolean isStudent = isCurrentStudent(educations);
        profile.setIsStudent(isStudent);

        // 获取毕业年份
        Integer graduationYear = getGraduationYear(educations);
        profile.setGraduationYear(graduationYear);
    }

    /**
     * 丰富个人数据
     */
    private void enrichPersonalData(LinkedinProfile profile, List<LinkedInScrapeResponse.Education> educations) {
        // 估算年龄
        Integer estimatedAge = estimateAge(profile.getBirthYear(), educations);
        if (estimatedAge != null) {
            profile.setAge(estimatedAge);
        }
    }

    /**
     * 生成公司和学校列表
     */
    private void enrichCompanyAndSchoolLists(LinkedinProfile profile, 
                                           List<LinkedInScrapeResponse.Experience> experiences,
                                           List<LinkedInScrapeResponse.Education> educations) {
        try {
            // 提取公司列表
            List<String> companyNames = extractCompanyList(experiences);
            if (!companyNames.isEmpty()) {
                profile.setCompanyList(objectMapper.writeValueAsString(companyNames));
            }

            // 提取学校列表
            List<String> schoolNames = extractSchoolList(educations);
            if (!schoolNames.isEmpty()) {
                profile.setSchoolList(objectMapper.writeValueAsString(schoolNames));
            }

        } catch (Exception e) {
            log.warn("Failed to serialize company/school lists: {}", e.getMessage());
        }
    }

    // ============ 日期解析相关方法 ============

    /**
     * 解析开始日期 - 对应Python的get_start_date()
     */
    private LocalDateTime parseStartDate(LinkedInScrapeResponse.DateRange dateRange) {
        if (dateRange == null || dateRange.getStart() == null) {
            return null;
        }

        return parseDateString(dateRange.getStart().trim(), true);
    }

    /**
     * 解析结束日期
     */
    private LocalDateTime parseEndDate(String dateStr) {
        return parseDateString(dateStr, false);
    }

    /**
     * 通用日期字符串解析方法
     * @param dateStr 日期字符串
     * @param isStartDate 是否为开始日期（影响默认日期设置）
     * @return 解析后的LocalDateTime
     */
    private LocalDateTime parseDateString(String dateStr, boolean isStartDate) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String trimmed = dateStr.trim();
        
        // 1. 尝试纯年份格式: "2023"
        if (trimmed.matches("\\d{4}")) {
            try {
                int year = Integer.parseInt(trimmed);
                if (isStartDate) {
                    return LocalDateTime.of(year, 1, 1, 0, 0);
                } else {
                    return LocalDateTime.of(year, 12, 31, 23, 59);
                }
            } catch (Exception e) {
                log.debug("Failed to parse as year: {}", trimmed);
            }
        }

        // 2. 尝试 "MMM yyyy" 格式: "Jul 2023", "May 2026"
        try {
            // 使用LocalDate解析，然后转换为LocalDateTime
            java.time.LocalDate date = java.time.LocalDate.parse("01 " + trimmed, 
                DateTimeFormatter.ofPattern("dd MMM yyyy", Locale.ENGLISH));
            if (isStartDate) {
                return date.atStartOfDay(); // 月初
            } else {
                return date.withDayOfMonth(date.lengthOfMonth()).atTime(23, 59, 59); // 月末
            }
        } catch (Exception e) {
            log.debug("Failed to parse as MMM yyyy: {}", trimmed);
        }

        // 3. 尝试 "MMMM yyyy" 格式: "December 2024"
        try {
            java.time.LocalDate date = java.time.LocalDate.parse("01 " + trimmed, 
                DateTimeFormatter.ofPattern("dd MMMM yyyy", Locale.ENGLISH));
            if (isStartDate) {
                return date.atStartOfDay();
            } else {
                return date.withDayOfMonth(date.lengthOfMonth()).atTime(23, 59, 59);
            }
        } catch (Exception e) {
            log.debug("Failed to parse as MMMM yyyy: {}", trimmed);
        }

        log.warn("Failed to parse date: {}", dateStr);
        return null;
    }

    /**
     * 判断是否为当前工作 - 对应Python的is_current_job()
     */
    private boolean isCurrentJob(LinkedInScrapeResponse.Experience experience) {
        if (experience.getDate() == null) {
            return false;
        }

        String end = experience.getDate().getEnd();
        
        // 没有结束日期 = 当前工作
        if (end == null || end.trim().isEmpty()) {
            return true;
        }

        // 检查是否为"present"、"current"等关键词
        String endLower = end.trim().toLowerCase();
        return endLower.equals("present") || 
               endLower.equals("current") || 
               endLower.equals("ongoing");
    }

    // ============ 工作经历分析方法 ============

    /**
     * 获取当前工作
     */
    private LinkedInScrapeResponse.Experience getCurrentJob(List<LinkedInScrapeResponse.Experience> experiences) {
        log.debug("🔍 Searching for current job among {} experiences", experiences.size());
        
        for (int i = 0; i < experiences.size(); i++) {
            LinkedInScrapeResponse.Experience exp = experiences.get(i);
            String title = exp.getTitle();
            String companyName = exp.getCompany() != null ? exp.getCompany().getName() : "No Company";
            String endDate = exp.getDate() != null ? exp.getDate().getEnd() : "No End Date";
            boolean isCurrent = isCurrentJob(exp);
            
            log.debug("🔍 Experience {}: {} at {} (end: '{}') → Current: {}", 
                i + 1, title, companyName, endDate, isCurrent);
        }
        
        LinkedInScrapeResponse.Experience currentJob = experiences.stream()
                .filter(this::isCurrentJob)
                .findFirst()
                .orElse(null);
                
        if (currentJob != null) {
            log.debug("✅ Found current job: {} at {}", currentJob.getTitle(), 
                currentJob.getCompany() != null ? currentJob.getCompany().getName() : "Unknown");
        } else {
            log.warn("❌ No current job found");
        }
        
        return currentJob;
    }

    /**
     * 计算总工作年限 - 对应Python的get_years_of_exp()
     */
    private double calculateTotalYearsOfExperience(List<LinkedInScrapeResponse.Experience> experiences) {
        List<LocalDateTime> startDates = experiences.stream()
                .map(exp -> parseStartDate(exp.getDate()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (startDates.isEmpty()) {
            return 0.0;
        }

        LocalDateTime earliestStart = startDates.stream().min(LocalDateTime::compareTo).orElse(null);
        if (earliestStart != null) {
            double years = ChronoUnit.DAYS.between(earliestStart, LocalDateTime.now()) / 365.0;
            return Math.round(years * 100.0) / 100.0;
        }

        return 0.0;
    }

    /**
     * 提取公司列表 - 对应Python的extract_company_list()
     */
    private List<String> extractCompanyList(List<LinkedInScrapeResponse.Experience> experiences) {
        return experiences.stream()
                .filter(exp -> exp.getCompany() != null && exp.getCompany().getName() != null)
                .map(exp -> exp.getCompany().getName())
                .distinct()
                .collect(Collectors.toList());
    }

    // ============ 教育背景分析方法 ============

    /**
     * 判断是否为在读学生 - 对应Python的is_student()
     */
    private boolean isCurrentStudent(List<LinkedInScrapeResponse.Education> educations) {
        LocalDateTime now = LocalDateTime.now();

        for (LinkedInScrapeResponse.Education education : educations) {
            if (education.getDate() == null) {
                continue;
            }

            String end = education.getDate().getEnd();
            
            // 没有结束日期 = 可能还在读
            if (end == null || end.trim().isEmpty()) {
                log.debug("🎓 No end date found → student = TRUE");
                return true;
            }

            // 检查是否为"present"等关键词
            String endLower = end.trim().toLowerCase();
            if (endLower.equals("present") || endLower.equals("ongoing") || endLower.equals("current")) {
                log.debug("🎓 Found 'present/ongoing/current' → student = TRUE");
                return true;
            }

            // 优先使用对象格式的精确日期比较（对应Python的dict处理）
            if (education.getDate().isEndInFuture()) {
                log.debug("🎓 End date in future (object format) → student = TRUE");
                return true;
            }

            // 尝试解析字符串格式的结束日期
            log.debug("🎓 Trying to parse end date: '{}'", end);
            try {
                LocalDateTime endDate = parseEndDate(end.trim());
                if (endDate != null) {
                    if (endDate.isAfter(now)) {
                        log.debug("🎓 End date in future (string format: {}) → student = TRUE", end);
                        return true; // 结束日期在未来 = 还在读
                    } else {
                        log.debug("🎓 End date in past (string format: {}) → continue checking", end);
                    }
                } else {
                    log.warn("🎓 Failed to parse end date: '{}'", end);
                }
            } catch (Exception e) {
                log.warn("🎓 Failed to parse end date '{}': {}", end, e.getMessage());
                // 解析失败，继续下一个教育经历
            }
        }

        log.debug("🎓 No future end dates found → student = FALSE");
        return false;
    }

    /**
     * 获取毕业年份 - 对应Python的get_graduation_year()
     */
    private Integer getGraduationYear(List<LinkedInScrapeResponse.Education> educations) {
        List<Integer> years = new ArrayList<>();

        for (LinkedInScrapeResponse.Education education : educations) {
            if (education.getDate() == null || education.getDate().getEnd() == null) {
                continue;
            }

            String end = education.getDate().getEnd();
            
            try {
                LocalDateTime endDate = parseEndDate(end);
                if (endDate != null) {
                    years.add(endDate.getYear());
                }
            } catch (Exception e) {
                log.debug("Failed to parse graduation year from: {}", end);
            }
        }

        return years.isEmpty() ? null : Collections.max(years);
    }

    /**
     * 获取最高学历 - 对应Python的get_highest_degree()
     */
    private String getHighestDegree(List<LinkedInScrapeResponse.Education> educations) {
        List<String> degreeHierarchy = Arrays.asList("doctor", "phd", "master", "bachelor", "associate");

        // 按开始日期倒序排序（最新的在前）
        List<LinkedInScrapeResponse.Education> sortedEducations = educations.stream()
                .sorted((e1, e2) -> {
                    LocalDateTime date1 = parseStartDate(e1.getDate());
                    LocalDateTime date2 = parseStartDate(e2.getDate());
                    if (date1 == null && date2 == null) return 0;
                    if (date1 == null) return 1;
                    if (date2 == null) return -1;
                    return date2.compareTo(date1); // 倒序
                })
                .collect(Collectors.toList());

        for (LinkedInScrapeResponse.Education education : sortedEducations) {
            String degree = education.getDegree();
            if (degree != null) {
                String degreeLower = degree.toLowerCase();
                for (String level : degreeHierarchy) {
                    if (degreeLower.contains(level)) {
                        return level;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取专业领域 - 对应Python的get_field_of_study()
     */
    private String getFieldOfStudy(List<LinkedInScrapeResponse.Education> educations) {
        // 从最新的教育经历开始查找
        for (int i = educations.size() - 1; i >= 0; i--) {
            LinkedInScrapeResponse.Education education = educations.get(i);
            String degree = education.getDegree();
            if (degree != null && !degree.trim().isEmpty()) {
                log.debug("🔍 Found field of study: {}", degree.trim());
                return degree.trim();
            }
        }
        return null;
    }

    /**
     * 提取学校列表 - 对应Python的extract_school_list()
     */
    private List<String> extractSchoolList(List<LinkedInScrapeResponse.Education> educations) {
        return educations.stream()
                .map(LinkedInScrapeResponse.Education::getSchool)
                .filter(school -> school != null && !school.trim().isEmpty())
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 估算年龄 - 对应Python的estimate_age()
     */
    private Integer estimateAge(Integer birthYear, List<LinkedInScrapeResponse.Education> educations) {
        int currentYear = LocalDateTime.now().getYear();

        // 1. 优先使用出生年份
        if (birthYear != null) {
            log.debug("🎂 Birth year found: {}", birthYear);
            return currentYear - birthYear;
        }

        log.debug("📚 No birth year, checking education dates...");

        // 2. 从教育经历估算
        List<Integer> startYears = new ArrayList<>();
        
        for (LinkedInScrapeResponse.Education education : educations) {
            LocalDateTime startDate = parseStartDate(education.getDate());
            if (startDate != null) {
                int startYear = startDate.getYear();
                log.debug("✅ Found education start year: {}", startYear);
                startYears.add(startYear);
            }
        }

        if (!startYears.isEmpty()) {
            int earliestYear = Collections.min(startYears);
            int estimatedAge = currentYear - earliestYear + 18; // 假设18岁开始上大学
            log.debug("🎓 Estimated age from earliest start year ({}): {}", earliestYear, estimatedAge);
            return estimatedAge;
        }

        log.debug("❌ No valid education start year found.");
        return null;
    }

    // ============ JSON解析辅助方法 ============

    /**
     * 解析工作经历JSON
     */
    private List<LinkedInScrapeResponse.Experience> parseExperiences(String experiencesJson) {
        try {
            if (experiencesJson == null || experiencesJson.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(experiencesJson, 
                new TypeReference<List<LinkedInScrapeResponse.Experience>>() {});
        } catch (Exception e) {
            log.warn("Failed to parse experiences JSON: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 解析教育经历JSON
     */
    private List<LinkedInScrapeResponse.Education> parseEducations(String educationsJson) {
        try {
            if (educationsJson == null || educationsJson.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(educationsJson, 
                new TypeReference<List<LinkedInScrapeResponse.Education>>() {});
        } catch (Exception e) {
            log.warn("Failed to parse educations JSON: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
} 