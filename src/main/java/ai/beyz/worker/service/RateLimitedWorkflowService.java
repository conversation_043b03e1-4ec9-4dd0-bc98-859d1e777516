package ai.beyz.worker.service;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

//@Service
@Slf4j
public class RateLimitedWorkflowService {

   // @Autowired
    private LinkedinScrapeServiceV2 linkedinScrapeServiceV2;

    // 每分钟30次 = 每秒0.5次
    private final RateLimiter rateLimiter = RateLimiter.create(0.5);
    
    // 任务队列，最大容量1000
    private final BlockingQueue<WorkflowTask> taskQueue = new LinkedBlockingQueue<>(5000);
    
    // 线程池执行器
    private ExecutorService executor;
    
    // 统计信息
    private final AtomicLong totalSubmitted = new AtomicLong(0);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);

    @PostConstruct
    public void init() {
        // 创建单线程执行器来处理队列
        executor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "workflow-rate-limiter");
            t.setDaemon(true);
            return t;
        });
        
        // 启动任务处理器
        executor.submit(this::processTaskQueue);
        
        log.info("🚀 RateLimitedWorkflowService initialized with rate limit: 30 requests/minute");
    }

    @PreDestroy
    public void shutdown() {
        if (executor != null) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("🛑 RateLimitedWorkflowService shutdown completed");
    }

    /**
     * 提交工作流任务（异步）
     */
    public CompletableFuture<String> submitWorkflowAsync(String email, String url) {
        CompletableFuture<String> future = new CompletableFuture<>();
        WorkflowTask task = new WorkflowTask(email, url, future);
        
        boolean queued = taskQueue.offer(task);
        if (queued) {
            totalSubmitted.incrementAndGet();
            log.debug("📋 Task queued for email: {} (queue size: {})", email, taskQueue.size());
            return future;
        } else {
            // 队列已满
            future.completeExceptionally(new RuntimeException("Task queue is full, please try again later"));
            log.warn("❌ Task queue is full, rejected task for email: {}", email);
            return future;
        }
    }

    /**
     * 提交工作流任务（同步，带超时）
     */
    public String submitWorkflowSync(String email, String url, long timeoutMinutes) throws Exception {
        CompletableFuture<String> future = submitWorkflowAsync(email, url);
        try {
            return future.get(timeoutMinutes, TimeUnit.MINUTES);
        } catch (TimeoutException e) {
            future.cancel(true);
            throw new RuntimeException("Workflow execution timed out after " + timeoutMinutes + " minutes");
        } catch (ExecutionException e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            } else {
                throw new RuntimeException("Workflow execution failed", e.getCause());
            }
        }
    }

    /**
     * 处理任务队列的主循环
     */
    private void processTaskQueue() {
        log.info("🔄 Starting task queue processor...");
        
        while (!Thread.currentThread().isInterrupted()) {
            try {
                // 从队列中取出任务（阻塞等待）
                WorkflowTask task = taskQueue.take();
                
                // 等待限流器许可
                log.debug("⏳ Waiting for rate limiter permit...");
                rateLimiter.acquire();
                
                // 执行任务
                processTask(task);
                
            } catch (InterruptedException e) {
                log.info("🛑 Task queue processor interrupted");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("❌ Error in task queue processor: {}", e.getMessage(), e);
                // 继续处理下一个任务
            }
        }
        
        log.info("🛑 Task queue processor stopped");
    }

    /**
     * 处理单个任务
     */
    private void processTask(WorkflowTask task) {
        try {
            log.info("🚀 Processing workflow task for email: {} (queue remaining: {})", 
                task.getEmail(), taskQueue.size());
            
            // 执行实际的工作流
            String result = linkedinScrapeServiceV2.workflow(task.getEmail(), task.getUrl());
            
            // 完成Future
            task.getFuture().complete(result);
            totalProcessed.incrementAndGet();
            
            log.info("✅ Workflow completed for email: {}, result: {}", task.getEmail(), result);
            
        } catch (Exception e) {
            log.error("❌ Workflow failed for email: {}: {}", task.getEmail(), e.getMessage(), e);
            task.getFuture().completeExceptionally(e);
            totalFailed.incrementAndGet();
        }
    }

    /**
     * 获取队列状态信息
     */
    public QueueStatus getQueueStatus() {
        return QueueStatus.builder()
                .queueSize(taskQueue.size())
                .queueCapacity(5000)
                .totalSubmitted(totalSubmitted.get())
                .totalProcessed(totalProcessed.get())
                .totalFailed(totalFailed.get())
                .rateLimit("30 requests/minute")
                .build();
    }

    /**
     * 工作流任务包装类
     */
    private static class WorkflowTask {
        private final String email;
        private final String url;
        private final CompletableFuture<String> future;

        public WorkflowTask(String email, String url, CompletableFuture<String> future) {
            this.email = email;
            this.url = url;
            this.future = future;
        }

        public String getEmail() { return email; }
        public String getUrl() { return url; }
        public CompletableFuture<String> getFuture() { return future; }
    }

    /**
     * 队列状态信息
     */
    @lombok.Builder
    @lombok.Data
    public static class QueueStatus {
        private int queueSize;
        private int queueCapacity;
        private long totalSubmitted;
        private long totalProcessed;
        private long totalFailed;
        private String rateLimit;
        private long remainingRequests;
    }
} 