<!-- src/main/resources/logback-spring.xml -->
<configuration>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="lokiUrl" source="loki.url"/>

    <!-- 控制台 Appender (用于本地开发) -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Loki Appender (用于部署到 Railway) -->
    <appender name="LOKI" class="com.github.loki4j.logback.Loki4jAppender">
        <http>
            <url>${lokiUrl}</url>
        </http>
        <format>
            <label>
                <pattern>app=${appName},host=${HOSTNAME},level=%level</pattern>
            </label>
            <message>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg %ex</pattern>
            </message>
            <sortByTime>true</sortByTime>
        </format>
    </appender>

    <!-- 本地开发环境 (dev) -->
    <springProfile name="default,dev">
        <root level="INFO">
<!--            <appender-ref ref="LOKI"/>-->
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <!-- Railway 生产环境 (prod) -->
    <springProfile name="production">
        <root level="INFO">
            <appender-ref ref="LOKI"/>
        </root>
    </springProfile>
</configuration>