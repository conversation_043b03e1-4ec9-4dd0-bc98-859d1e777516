spring:
  application:
    name: scrape-worker
  datasource:
    profile:
      driver-class-name: org.postgresql.Driver
      url: ${PROFILE_DATABASE_URL:*********************************************************************************************}
      username: ${PROFILE_DATABASE_USERNAME:neondb_owner}
      password: ${PROFILE_DATABASE_PASSWORD:npg_S7n6sQhcwGBD}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    beyz:
      driver-class-name: org.postgresql.Driver
      url: ${BEYZ_DATABASE_URL:**********************************************************************************************************}
      username: ${BEYZ_DATABASE_USERNAME:beyz-web_owner}
      password: ${BEYZ_DATABASE_PASSWORD:M3fLGusc9SIV}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        globally_quoted_identifiers: true
        show_sql: false
        format_sql: false
    show-sql: false

  #aws
  cloud:
    aws:
      region:
        static: ${AWS_REGION:ap-northeast-1}
      credentials:
        access-key: ${AWS_ACCESS_KEY:********************}
        secret-key: ${AWS_SECRET_KEY:jVaHaCBbi9hqPBaheapUxNbxFe06dXmsa/+0PwNA}
      sqs:
        endpoint: ${AWS_SQS_ENDPOINT:https://sqs.ap-northeast-1.amazonaws.com/353654037226/linkedin_url_queue}

# 生产环境日志配置
logging:
  level:
    root: INFO
    ai.beyz: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

# 服务器配置
server:
  port: ${PORT:8080}
  shutdown: graceful
  tomcat:
    max-threads: 200
    min-spare-threads: 10

# Loki配置
loki:
  url: ${LOKI_URL:https://loki-production-555f.up.railway.app/loki/api/v1/push}

# API配置
app:
  api:
    email-verifier:
      url: ${EMAIL_VERIFIER_URL:https://api.millionverifier.com/api/v3/?api=uRRSTeKLdlQAiQGBCfp0Cu5u1&email={email}&timeout=10}
    linkedin-scraper:
      url: ${LINKEDIN_SCRAPER_URL:https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile}
      key: ${LINKEDIN_SCRAPER_KEY:d4f97cc811msh247a487d06a4331p1abd28jsnd41d7e6ac579}
      host: ${LINKEDIN_SCRAPER_HOST:fresh-linkedin-scraper-api.p.rapidapi.com}
    google:
      key: ${GOOGLE_API_KEY:AIzaSyBtD78_wEfkqTA5o1tYIKSgUtb0n9KBogg}
      model: ${GOOGLE_MODEL:gemini-2.0-flash-001}
    cloudflare:
      account-id: "b44e0e948392051cb0cfee297cb61d92"
      api-token: "KwGlPKA7YR5zZiXmfzTpbMfaHWyOFoBKbheffLai"
      kv-namespace-id: "2bcf266983204074a99ab172847fbce7" # default namespace
      base-url: "https://api.cloudflare.com/client/v4"
      namespaces:
        email-verifier: "4ce31ad85173494984728841cb41079e"
        linkedin-verifier: "2bcf266983204074a99ab172847fbce7"
  meilisearch:
    url: "https://ms-e926f38a7bfc-24685.nyc.meilisearch.io"
    key: "ea76099ba72078a4a4ed12a59209764595454a6c"
    index: "bluelet"