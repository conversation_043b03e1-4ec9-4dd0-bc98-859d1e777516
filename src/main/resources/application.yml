spring:
  application:
    name: scrape-worker
  datasource:
    profile:
      driver-class-name: org.postgresql.Driver
      url: *********************************************************************************************
      username: neondb_owner
      password: npg_S7n6sQhcwGBD
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    beyz:
      driver-class-name: org.postgresql.Driver
      url: **********************************************************************************************************
      username: beyz-web_owner
      password: M3fLGusc9SIV
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        globally_quoted_identifiers: true
        show_sql: true
        format_sql: false
  ai:
    openai:
      api-key: ********************************************************************************************************************************************************************
      embedding:
        options:
          model: text-embedding-3-small
    vectorstore:
      pgvector:
        index-type: hnsw
        dimensions: 1536
        distance-type: COSINE_DISTANCE


  #aws
  cloud:
    aws:
      region:
        static: ap-northeast-1
      credentials:
        access-key: ********************
        secret-key: jVaHaCBbi9hqPBaheapUxNbxFe06dXmsa/+0PwNA
      sqs:
        endpoint: https://sqs.ap-northeast-1.amazonaws.com/353654037226/linkedin_url_queue
logging:
  level:
    ai.beyz: debug
loki:
  url: https://loki-production-555f.up.railway.app/loki/api/v1/push

app:
  api:
    email-verifier:
      url: "https://api.millionverifier.com/api/v3/?api=uRRSTeKLdlQAiQGBCfp0Cu5u1&email={email}&timeout=10"
    linkedin-scraper:
      url: "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile"
      key: "**************************************************"
      host: "fresh-linkedin-scraper-api.p.rapidapi.com"
    google:
      key: "AIzaSyBtD78_wEfkqTA5o1tYIKSgUtb0n9KBogg"
      model: "gemini-2.0-flash-001"
    openai:
      key: "********************************************************************************************************************************************************************"
      model: "text-embedding-3-small"
    cloudflare:
      account-id: "b44e0e948392051cb0cfee297cb61d92"
      api-token: "KwGlPKA7YR5zZiXmfzTpbMfaHWyOFoBKbheffLai"
      kv-namespace-id: "2bcf266983204074a99ab172847fbce7" # default namespace
      base-url: "https://api.cloudflare.com/client/v4"
      namespaces:
        email-verifier: "4ce31ad85173494984728841cb41079e"
        linkedin-verifier: "2bcf266983204074a99ab172847fbce7"
  meilisearch:
    url: "https://ms-e926f38a7bfc-24685.nyc.meilisearch.io"
    key: "ea76099ba72078a4a4ed12a59209764595454a6c"
    index: "bluelet"
    