package ai.beyz.worker.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.util.concurrent.RateLimiter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.*;
import java.util.concurrent.Executor;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class BatchLinkedinScrapeServiceTest {

    @Mock
    private SendDataToQueueService sendDataToQueueService;

    @Mock
    private LinkedinScrapeServiceV2 linkedinScrapeServiceV2;

    @Mock
    private RateLimiter rateLimiter;

    @Mock
    private Executor threadPoolExecutor;

    @InjectMocks
    private BatchLinkedinScrapeService batchLinkedinScrapeService;

    private List<Message<String>> createTestMessages(int count) {
        List<Message<String>> messages = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            Map<String, String> data = new HashMap<>();
            data.put("email", "test" + i + "@example.com");
            data.put("url", "https://www.linkedin.com/in/test" + i);
            
            String payload = JSON.toJSONString(data);
            messages.add(MessageBuilder.withPayload(payload).build());
        }
        return messages;
    }

    @BeforeEach
    void setUp() {
        // 模拟RateLimiter行为 - acquire()方法返回double，表示等待时间
        lenient().when(rateLimiter.acquire()).thenReturn(0.0);

        // 模拟线程池执行器 - 直接在当前线程执行任务（同步执行）
        lenient().doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 直接在当前线程执行，避免异步复杂性
            return null;
        }).when(threadPoolExecutor).execute(any(Runnable.class));
    }

    @Test
    void testProcessBatchLinkedinScrape_EmptyData() {
        // 模拟没有数据的情况
        when(sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000))
                .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> batchLinkedinScrapeService.processBatchLinkedinScrape());

        // 验证调用
        verify(sendDataToQueueService, times(1)).findUnscrapedWithEmailAndLinkedinUrl(1000);
        verify(linkedinScrapeServiceV2, never()).workflow(anyString(), anyString());
    }

    @Test
    void testProcessBatchLinkedinScrape_SingleBatch() {
        // 准备测试数据
        List<Message<String>> testMessages = createTestMessages(5);
        
        when(sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000))
                .thenReturn(testMessages)
                .thenReturn(Collections.emptyList()); // 第二次调用返回空列表

        // 模拟workflow方法返回成功
        when(linkedinScrapeServiceV2.workflow(anyString(), anyString()))
                .thenReturn("talent-12345");

        // 执行测试
        assertDoesNotThrow(() -> batchLinkedinScrapeService.processBatchLinkedinScrape());

        // 验证调用次数
        verify(sendDataToQueueService, times(2)).findUnscrapedWithEmailAndLinkedinUrl(1000);
        verify(linkedinScrapeServiceV2, times(5)).workflow(anyString(), anyString());
        verify(rateLimiter, times(5)).acquire();
    }

    @Test
    void testProcessBatchLinkedinScrape_MultipleBatches() {
        // 准备测试数据 - 模拟两个完整批次
        List<Message<String>> batch1 = createTestMessages(1000);
        List<Message<String>> batch2 = createTestMessages(500);
        
        when(sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000))
                .thenReturn(batch1)
                .thenReturn(batch2)
                .thenReturn(Collections.emptyList());

        // 模拟workflow方法返回成功
        when(linkedinScrapeServiceV2.workflow(anyString(), anyString()))
                .thenReturn("talent-12345");

        // 执行测试
        assertDoesNotThrow(() -> batchLinkedinScrapeService.processBatchLinkedinScrape());

        // 验证调用次数
        verify(sendDataToQueueService, times(3)).findUnscrapedWithEmailAndLinkedinUrl(1000);
        verify(linkedinScrapeServiceV2, times(1500)).workflow(anyString(), anyString());
        verify(rateLimiter, times(1500)).acquire();
    }

    @Test
    void testProcessBatchLinkedinScrape_WithFailures() {
        // 准备测试数据
        List<Message<String>> testMessages = createTestMessages(3);
        
        when(sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000))
                .thenReturn(testMessages)
                .thenReturn(Collections.emptyList());

        // 模拟部分成功，部分失败
        when(linkedinScrapeServiceV2.workflow("<EMAIL>", "https://www.linkedin.com/in/test1"))
                .thenReturn("talent-12345");
        when(linkedinScrapeServiceV2.workflow("<EMAIL>", "https://www.linkedin.com/in/test2"))
                .thenReturn(null); // 失败
        when(linkedinScrapeServiceV2.workflow("<EMAIL>", "https://www.linkedin.com/in/test3"))
                .thenThrow(new RuntimeException("API Error")); // 异常

        // 执行测试
        assertDoesNotThrow(() -> batchLinkedinScrapeService.processBatchLinkedinScrape());

        // 验证调用次数
        verify(linkedinScrapeServiceV2, times(3)).workflow(anyString(), anyString());
        verify(rateLimiter, times(3)).acquire();
    }

    @Test
    void testProcessBatchLinkedinScrape_InvalidMessageFormat() {
        // 创建无效格式的消息
        List<Message<String>> invalidMessages = Arrays.asList(
                MessageBuilder.withPayload("invalid json").build(),
                MessageBuilder.withPayload("{\"email\":\"<EMAIL>\"}").build(), // 缺少url
                MessageBuilder.withPayload("{\"url\":\"https://linkedin.com/in/test\"}").build() // 缺少email
        );
        
        when(sendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000))
                .thenReturn(invalidMessages)
                .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> batchLinkedinScrapeService.processBatchLinkedinScrape());

        // 验证没有调用workflow方法
        verify(linkedinScrapeServiceV2, never()).workflow(anyString(), anyString());
        verify(rateLimiter, never()).acquire();
    }

    @Test
    void testGetProcessingStatus() {
        // 执行测试
        Map<String, Object> status = batchLinkedinScrapeService.getProcessingStatus();

        // 验证返回的状态信息
        assertNotNull(status);
        assertEquals("30 requests/minute (1 request/second)", status.get("rateLimitConfig"));
        assertEquals(1000, status.get("batchSize"));
        assertEquals(false, status.get("isRunning"));
    }

    @Test
    void testParseMessageData_ValidJson() throws Exception {
        // 使用反射访问私有方法进行测试
        var method = BatchLinkedinScrapeService.class.getDeclaredMethod("parseMessageData", String.class);
        method.setAccessible(true);

        Map<String, String> testData = new HashMap<>();
        testData.put("email", "<EMAIL>");
        testData.put("url", "https://linkedin.com/in/test");
        String validJson = JSON.toJSONString(testData);

        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) method.invoke(batchLinkedinScrapeService, validJson);

        assertNotNull(result);
        assertEquals("<EMAIL>", result.get("email"));
        assertEquals("https://linkedin.com/in/test", result.get("url"));
    }

    @Test
    void testParseMessageData_InvalidJson() throws Exception {
        // 使用反射访问私有方法进行测试
        var method = BatchLinkedinScrapeService.class.getDeclaredMethod("parseMessageData", String.class);
        method.setAccessible(true);

        Map<String, String> result = (Map<String, String>) method.invoke(batchLinkedinScrapeService, "invalid json");

        assertNull(result);
    }
}
