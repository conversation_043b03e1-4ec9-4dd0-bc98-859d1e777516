#!/bin/bash

# LinkedIn 批量爬取工具启动脚本
# 使用方法: ./run-batch-scrape.sh [mode] [profile]
# mode: auto (自动模式) 或 interactive (交互模式，默认)
# profile: dev, test, prod (默认: prod)

set -e

# 默认参数
MODE="interactive"
PROFILE="prod"
JAR_FILE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        auto|interactive)
            MODE="$1"
            shift
            ;;
        dev|test|prod)
            PROFILE="$1"
            shift
            ;;
        --jar=*)
            JAR_FILE="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "LinkedIn 批量爬取工具启动脚本"
            echo ""
            echo "使用方法: $0 [mode] [profile] [options]"
            echo ""
            echo "参数:"
            echo "  mode        运行模式 (auto|interactive, 默认: interactive)"
            echo "  profile     Spring配置文件 (dev|test|prod, 默认: prod)"
            echo ""
            echo "选项:"
            echo "  --jar=FILE  指定JAR文件路径"
            echo "  -h, --help  显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 auto prod                    # 生产环境自动模式"
            echo "  $0 interactive dev              # 开发环境交互模式"
            echo "  $0 auto --jar=target/app.jar    # 指定JAR文件"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 查找JAR文件
if [[ -z "$JAR_FILE" ]]; then
    if [[ -f "target/worker-1.0-SNAPSHOT.jar" ]]; then
        JAR_FILE="target/worker-1.0-SNAPSHOT.jar"
    elif [[ -f "target/worker.jar" ]]; then
        JAR_FILE="target/worker.jar"
    else
        # 查找target目录下的任何worker*.jar文件
        JAR_FILE=$(find target -name "worker*.jar" -type f | head -1)
        if [[ -z "$JAR_FILE" ]]; then
            echo "❌ 错误: 找不到JAR文件"
            echo "请先运行: mvn clean package -DskipTests"
            exit 1
        fi
    fi
fi

# 检查JAR文件是否存在
if [[ ! -f "$JAR_FILE" ]]; then
    echo "❌ 错误: JAR文件不存在: $JAR_FILE"
    echo "请先运行: mvn clean package -DskipTests"
    exit 1
fi

# 显示配置信息
echo "🚀 LinkedIn 批量爬取工具"
echo "=========================="
echo "📁 JAR文件: $JAR_FILE"
echo "🔧 运行模式: $MODE"
echo "🌍 环境配置: $PROFILE"
echo "=========================="

# 构建Java命令
JAVA_OPTS="-Xmx2g -Xms512m"
SPRING_OPTS="--spring.profiles.active=$PROFILE --spring.main.web-application-type=none"
MAIN_CLASS="ai.beyz.worker.cli.LinkedinScrapeCliApplication"

if [[ "$MODE" == "auto" ]]; then
    ARGS="auto"
else
    ARGS=""
fi

# 执行命令
echo "🎯 启动命令:"
echo "java $JAVA_OPTS -jar $JAR_FILE $SPRING_OPTS $MAIN_CLASS $ARGS"
echo ""

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | head -1 | cut -d'"' -f2 | sed '/^1\./s///' | cut -d'.' -f1)
if [[ "$JAVA_VERSION" -lt 17 ]]; then
    echo "⚠️  警告: 检测到Java版本 $JAVA_VERSION，建议使用Java 17或更高版本"
fi

echo "⏳ 正在启动..."
echo ""

# 运行应用
exec java $JAVA_OPTS -jar "$JAR_FILE" $SPRING_OPTS $MAIN_CLASS $ARGS
