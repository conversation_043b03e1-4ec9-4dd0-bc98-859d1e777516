@echo off
setlocal enabledelayedexpansion

REM LinkedIn 批量爬取工具启动脚本 (Windows版本)
REM 使用方法: run-batch-scrape.bat [mode] [profile]
REM mode: auto (自动模式) 或 interactive (交互模式，默认)
REM profile: dev, test, prod (默认: prod)

REM 默认参数
set MODE=interactive
set PROFILE=prod
set JAR_FILE=

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :find_jar
if "%~1"=="auto" (
    set MODE=auto
    shift
    goto :parse_args
)
if "%~1"=="interactive" (
    set MODE=interactive
    shift
    goto :parse_args
)
if "%~1"=="dev" (
    set PROFILE=dev
    shift
    goto :parse_args
)
if "%~1"=="test" (
    set PROFILE=test
    shift
    goto :parse_args
)
if "%~1"=="prod" (
    set PROFILE=prod
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="/?" goto :show_help

echo 未知参数: %~1
echo 使用 -h 或 --help 查看帮助信息
exit /b 1

:show_help
echo LinkedIn 批量爬取工具启动脚本
echo.
echo 使用方法: %~nx0 [mode] [profile]
echo.
echo 参数:
echo   mode        运行模式 (auto^|interactive, 默认: interactive)
echo   profile     Spring配置文件 (dev^|test^|prod, 默认: prod)
echo.
echo 选项:
echo   -h, --help  显示此帮助信息
echo.
echo 示例:
echo   %~nx0 auto prod                    # 生产环境自动模式
echo   %~nx0 interactive dev              # 开发环境交互模式
exit /b 0

:find_jar
REM 查找JAR文件
if exist "target\worker-1.0-SNAPSHOT.jar" (
    set JAR_FILE=target\worker-1.0-SNAPSHOT.jar
) else if exist "target\worker.jar" (
    set JAR_FILE=target\worker.jar
) else (
    REM 查找target目录下的任何worker*.jar文件
    for %%f in (target\worker*.jar) do (
        set JAR_FILE=%%f
        goto :check_jar
    )
    echo ❌ 错误: 找不到JAR文件
    echo 请先运行: mvn clean package -DskipTests
    exit /b 1
)

:check_jar
REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo ❌ 错误: JAR文件不存在: %JAR_FILE%
    echo 请先运行: mvn clean package -DskipTests
    exit /b 1
)

REM 显示配置信息
echo 🚀 LinkedIn 批量爬取工具
echo ==========================
echo 📁 JAR文件: %JAR_FILE%
echo 🔧 运行模式: %MODE%
echo 🌍 环境配置: %PROFILE%
echo ==========================

REM 构建Java命令
set JAVA_OPTS=-Xmx2g -Xms512m
set SPRING_OPTS=--spring.profiles.active=%PROFILE% --spring.main.web-application-type=none
set MAIN_CLASS=ai.beyz.worker.cli.LinkedinScrapeCliApplication

if "%MODE%"=="auto" (
    set ARGS=auto
) else (
    set ARGS=
)

REM 检查Java是否安装
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Java运行环境
    echo 请确保已安装Java 17或更高版本，并添加到PATH环境变量中
    exit /b 1
)

REM 显示启动命令
echo 🎯 启动命令:
echo java %JAVA_OPTS% -jar %JAR_FILE% %SPRING_OPTS% %MAIN_CLASS% %ARGS%
echo.

REM 检查Java版本
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION_STRING=%%g
)
set JAVA_VERSION_STRING=%JAVA_VERSION_STRING:"=%
for /f "delims=. tokens=1-3" %%v in ("%JAVA_VERSION_STRING%") do (
    if "%%v"=="1" (
        set JAVA_MAJOR_VERSION=%%w
    ) else (
        set JAVA_MAJOR_VERSION=%%v
    )
)

if %JAVA_MAJOR_VERSION% LSS 17 (
    echo ⚠️  警告: 检测到Java版本 %JAVA_MAJOR_VERSION%，建议使用Java 17或更高版本
)

echo ⏳ 正在启动...
echo.

REM 运行应用
java %JAVA_OPTS% -jar "%JAR_FILE%" %SPRING_OPTS% %MAIN_CLASS% %ARGS%
