# Railway 部署指南

## 前置条件

1. 拥有 Railway 账户
2. 项目代码已推送到 Git 仓库（GitHub, GitLab, 或 Bitbucket）

## 部署步骤

### 1. 连接到 Railway

1. 访问 [railway.app](https://railway.app)
2. 使用 GitHub 账户登录
3. 点击 "New Project"
4. 选择 "Deploy from GitHub repo"
5. 选择你的项目仓库

### 2. 配置环境变量

在 Railway 项目设置中添加以下环境变量：

#### 数据库配置
```
PROFILE_DATABASE_URL=***************************************************************
PROFILE_DATABASE_USERNAME=your-username
PROFILE_DATABASE_PASSWORD=your-password

BEYZ_DATABASE_URL=************************************************************
BEYZ_DATABASE_USERNAME=your-username
BEYZ_DATABASE_PASSWORD=your-password
```

#### AWS 配置
```
AWS_REGION=ap-northeast-1
AWS_ACCESS_KEY=your-access-key
AWS_SECRET_KEY=your-secret-key
AWS_SQS_ENDPOINT=https://sqs.region.amazonaws.com/account/queue-name
```

#### API 配置
```
EMAIL_VERIFIER_URL=https://api.millionverifier.com/api/v3/?api=your-api-key&email={email}&timeout=10
LINKEDIN_SCRAPER_URL=https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile
LINKEDIN_SCRAPER_KEY=your-rapidapi-key
LINKEDIN_SCRAPER_HOST=fresh-linkedin-scraper-api.p.rapidapi.com
GOOGLE_API_KEY=your-google-api-key
GOOGLE_MODEL=gemini-2.0-flash-001
```

#### 其他配置
```
LOKI_URL=https://your-loki-instance/loki/api/v1/push
PORT=8080
```

### 3. 部署配置

Railway 会自动检测到 `Dockerfile` 并使用 Docker 构建。

### 4. 监控部署

1. 在 Railway 控制台查看构建日志
2. 部署完成后，访问健康检查端点：`https://your-app.railway.app/actuator/health`

## 部署后验证

### 健康检查
```bash
curl https://your-app.railway.app/actuator/health
```

### 应用信息
```bash
curl https://your-app.railway.app/actuator/info
```

## 故障排除

### 常见问题

1. **构建失败**
   - 检查 Java 版本是否为 17
   - 确保所有依赖都能正确下载

2. **启动失败**
   - 检查环境变量是否正确设置
   - 查看应用日志中的错误信息

3. **数据库连接失败**
   - 验证数据库 URL 和凭据
   - 确保数据库允许来自 Railway 的连接

### 查看日志
在 Railway 控制台的 "Deployments" 标签页可以查看实时日志。

## 扩展配置

### 自动扩展
Railway 支持根据负载自动扩展实例数量。

### 自定义域名
可以在 Railway 控制台配置自定义域名。

### 备份策略
建议定期备份数据库，可以使用 Railway 的数据库备份功能。

## 安全建议

1. 不要在代码中硬编码敏感信息
2. 使用强密码和定期轮换 API 密钥
3. 启用数据库 SSL 连接
4. 定期更新依赖项以修复安全漏洞 