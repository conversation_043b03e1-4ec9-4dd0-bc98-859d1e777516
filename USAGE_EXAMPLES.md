# LinkedIn 批量爬取工具使用示例

## 🚀 快速开始示例

### 1. 编译和准备

```bash
# 编译项目
mvn clean package -DskipTests

# 确认JAR文件生成
ls -la target/worker*.jar
```

### 2. 命令行使用示例

#### 自动模式（生产环境推荐）
```bash
# Linux/Mac
./run-batch-scrape.sh auto prod

# Windows
run-batch-scrape.bat auto prod

# 直接使用Java命令
java -Xmx2g -jar target/worker-*.jar \
  --spring.profiles.active=prod \
  --spring.main.web-application-type=none \
  ai.beyz.worker.cli.LinkedinScrapeCliApplication auto
```

#### 交互模式（开发测试推荐）
```bash
# Linux/Mac
./run-batch-scrape.sh interactive dev

# Windows
run-batch-scrape.bat interactive dev

# 然后在交互界面中输入命令
Enter command: start
```

### 3. Web API 使用示例

#### 启动批处理
```bash
curl -X POST http://localhost:8080/api/v1/batch/linkedin-scrape/start \
  -H "Content-Type: application/json"
```

响应示例：
```json
{
  "status": "success",
  "message": "Batch processing started successfully",
  "isProcessing": true,
  "note": "This is an asynchronous operation. Use /status endpoint to check progress."
}
```

#### 查看处理状态
```bash
curl http://localhost:8080/api/v1/batch/linkedin-scrape/status
```

响应示例：
```json
{
  "rateLimitConfig": "30 requests/minute (0.5 requests/second)",
  "batchSize": 1000,
  "isRunning": false,
  "isProcessing": false,
  "hasActiveTask": false,
  "status": "success"
}
```

#### 停止处理
```bash
curl -X POST http://localhost:8080/api/v1/batch/linkedin-scrape/stop
```

#### 查看配置信息
```bash
curl http://localhost:8080/api/v1/batch/linkedin-scrape/config
```

## 📊 实际运行示例

### 命令行输出示例

```
🚀 LinkedIn 批量爬取工具
==========================
📁 JAR文件: target/worker-1.0-SNAPSHOT.jar
🔧 运行模式: auto
🌍 环境配置: prod
==========================

🚀 Starting batch LinkedIn scraping process...
📦 Processing batch #1 (fetching 1000 records)...
📋 Found 1000 records in batch #1
🔄 Processing item 1/1000 - Email: <EMAIL>, URL: https://www.linkedin.com/in/johndoe
✅ Successfully processed: <EMAIL> -> talent-abc123
🔄 Processing item 2/1000 - Email: <EMAIL>, URL: https://www.linkedin.com/in/janesmith
✅ Successfully processed: <EMAIL> -> talent-def456
...
📊 Progress: 10/1000 items processed in batch #1 (avg: 2150.50ms/item)
...
📈 Batch #1 completed: 1000 items processed (Success: 950, Failed: 50, Total time: 2150500ms)

📦 Processing batch #2 (fetching 1000 records)...
📋 Found 500 records in batch #2
...
✅ Reached end of data (batch size: 500 < 1000). Processing completed!

🏁 Batch LinkedIn scraping completed!
📊 Final Statistics:
   - Total Batches: 2
   - Total Processed: 1500
   - Total Success: 1425
   - Total Failed: 75
   - Success Rate: 95.00%
   - Total Time: 3600.50 seconds
   - Average Time per Item: 2400.33ms
   - Processing Rate: 25.00 items/minute
```

### 交互模式示例

```
================================================================================
🔧 LinkedIn Batch Scraping Tool
================================================================================
📋 This tool will:
   1. Fetch unscraped data from database (1000 records per batch)
   2. Process each record using LinkedIn scraping workflow
   3. Apply rate limiting (30 requests per minute)
   4. Continue until all data is processed
================================================================================
⚙️ Configuration:
   - Rate Limit: 30 requests/minute (0.5 requests/second)
   - Batch Size: 1000
================================================================================

📝 Available commands:
   1. start  - Start batch processing
   2. status - Show current status
   3. help   - Show this help message
   4. exit   - Exit the application
----------------------------------------
Enter command: status

📊 Current Status:
   - rateLimitConfig: 30 requests/minute (0.5 requests/second)
   - batchSize: 1000
   - isRunning: false

Enter command: start

🚀 Starting batch LinkedIn scraping process...
⚠️  This process may take a long time depending on the amount of data.
⚠️  The process will respect rate limits (30 requests/minute).
⚠️  Press Ctrl+C to stop the process if needed.
------------------------------------------------------------
...
```

## 🔧 配置示例

### application.yml 配置
```yaml
# 数据库配置
spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password

# LinkedIn API配置
app:
  api:
    linkedin-scraper:
      url: "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile"
      key: "your-rapidapi-key"
      host: "fresh-linkedin-scraper-api.p.rapidapi.com"
    email-verifier:
      url: "https://api.millionverifier.com/api/v3/?api=your-api-key&email={email}&timeout=10"

# 日志配置
logging:
  level:
    ai.beyz: INFO
    root: WARN
```

### 环境变量配置
```bash
# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod
export JAVA_OPTS="-Xmx2g -Xms512m"
export DB_URL="****************************************"
export DB_USERNAME="your_username"
export DB_PASSWORD="your_password"
export LINKEDIN_API_KEY="your-rapidapi-key"
```

## 🐳 Docker 使用示例

### Dockerfile
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY target/worker-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-Xmx2g", "-jar", "app.jar"]
CMD ["ai.beyz.worker.cli.LinkedinScrapeCliApplication", "auto"]
```

### 构建和运行
```bash
# 构建Docker镜像
docker build -t linkedin-batch-scraper .

# 运行容器（自动模式）
docker run -d \
  --name linkedin-scraper \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_URL=*************************************************** \
  -e DB_USERNAME=your_username \
  -e DB_PASSWORD=your_password \
  linkedin-batch-scraper

# 查看日志
docker logs -f linkedin-scraper

# 停止容器
docker stop linkedin-scraper
```

## 📈 监控和调试示例

### 查看实时日志
```bash
# 跟踪日志文件
tail -f logs/application.log

# 过滤特定日志
tail -f logs/application.log | grep "Processing item"

# 统计成功率
tail -f logs/application.log | grep -E "(Successfully processed|Failed to process)" | wc -l
```

### 性能监控
```bash
# 监控Java进程
jps -l | grep LinkedinScrapeCliApplication

# 查看内存使用
jstat -gc <pid> 5s

# 查看线程状态
jstack <pid>
```

### 数据库查询示例
```sql
-- 查看未处理的记录数量
SELECT COUNT(*) FROM mailingList 
WHERE scraped = false 
  AND email IS NOT NULL 
  AND email != '' 
  AND linkedinUrl IS NOT NULL 
  AND linkedinUrl != '' 
  AND status = 0 
  AND linkedinUrl LIKE 'https://www.linkedin.com/in/%';

-- 查看处理进度
SELECT 
  COUNT(*) as total,
  SUM(CASE WHEN scraped = true THEN 1 ELSE 0 END) as processed,
  SUM(CASE WHEN scraped = false THEN 1 ELSE 0 END) as remaining,
  ROUND(SUM(CASE WHEN scraped = true THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as progress_percent
FROM mailingList 
WHERE email IS NOT NULL 
  AND linkedinUrl IS NOT NULL;

-- 查看最近处理的记录
SELECT email, linkedinUrl, scraped, updatedAt, status, comments
FROM mailingList 
WHERE updatedAt > NOW() - INTERVAL '1 hour'
ORDER BY updatedAt DESC
LIMIT 10;
```

## 🚨 故障排查示例

### 常见错误和解决方案

#### 1. 数据库连接错误
```
错误: Connection refused
解决: 检查数据库配置和网络连接
```

#### 2. API限流错误
```
错误: Rate limit exceeded
解决: 检查RateLimiter配置，确保每分钟不超过30次调用
```

#### 3. 内存不足错误
```
错误: OutOfMemoryError
解决: 增加JVM内存 -Xmx4g 或减少批处理大小
```

#### 4. 网络超时错误
```
错误: Read timeout
解决: 增加超时时间或检查网络稳定性
```
